# Docker完整架构：MySQL + Redis + Celery + FastAPI + Nginx + Frontend
# 说明：所有服务运行在Docker容器中，数据库仅内部访问，不对外暴露端口
services:
  # MySQL数据库服务 - 仅内部访问
  mysql:
    image: mysql:latest
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - TZ=${TZ:-Asia/Shanghai}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init_database.sql:/docker-entrypoint-initdb.d/init_database.sql:ro
    # 注意：不暴露端口，确保数据库仅内部访问
    # ports:
    #   - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  redis:
    image: redis:latest
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    # Redis也仅内部访问
    # ports:
    #   - "${REDIS_PORT:-6379}:6379"
    volumes:
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    env_file: .env
    environment:
      - DATABASE_URL=mysql+pymysql://${MYSQL_USER}:${MYSQL_ROOT_PASSWORD}@mysql:3306/${MYSQL_DATABASE}?charset=utf8mb4
      - REDIS_URL=redis://redis:6379/0
      - TZ=${TZ:-Asia/Shanghai}
      - DOCKER_ENV=true
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./backend/app:/app/app:ro  # 开发模式：挂载代码目录
    restart: unless-stopped

  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: ["celery", "-A", "app.celery_app.celery_app", "worker", "-l", "info", "--queues=default,crawler,processor,pusher"]
    env_file: .env
    environment:
      - DATABASE_URL=mysql+pymysql://${MYSQL_USER}:${MYSQL_ROOT_PASSWORD}@mysql:3306/${MYSQL_DATABASE}?charset=utf8mb4
      - REDIS_URL=redis://redis:6379/0
      - TZ=${TZ:-Asia/Shanghai}
      - DOCKER_ENV=true
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      backend:
        condition: service_started
    restart: unless-stopped

  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: ["celery", "-A", "app.celery_app.celery_app", "beat", "-l", "info", "--scheduler=celery.beat:PersistentScheduler"]
    env_file: .env
    environment:
      - DATABASE_URL=mysql+pymysql://${MYSQL_USER}:${MYSQL_ROOT_PASSWORD}@mysql:3306/${MYSQL_DATABASE}?charset=utf8mb4
      - REDIS_URL=redis://redis:6379/0
      - TZ=${TZ:-Asia/Shanghai}
      - DOCKER_ENV=true
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      backend:
        condition: service_started
    volumes:
      - celery-beat-data:/app/beat-data
    restart: unless-stopped

  frontend:
    build: ./frontend
    depends_on:
      backend:
        condition: service_started
    # 前端容器不直接暴露端口，只通过nginx代理访问
    # ports:
    #   - "${FRONTEND_PORT:-80}:80"
    restart: unless-stopped

  # 注意：安全监控和合规监控功能已集成到backend服务中
  # 原独立的 security-monitor 和 compliance-monitor 微服务已移除
  # 相关功能现在通过 /api/v1/monitoring 接口提供

  nginx:
    build: ./nginx
    depends_on:
      frontend:
        condition: service_started
      backend:
        condition: service_started
    ports:
      - "${NGINX_PORT:-8080}:80"
    restart: unless-stopped

volumes:
  mysql_data:
    driver: local
  celery-beat-data:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: financial_news_bot_net
    driver: bridge

