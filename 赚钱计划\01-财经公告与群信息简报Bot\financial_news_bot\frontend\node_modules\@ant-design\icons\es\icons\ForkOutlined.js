import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ForkOutlinedSvg from "@ant-design/icons-svg/es/asn/ForkOutlined";
import AntdIcon from "../components/AntdIcon";
var ForkOutlined = function ForkOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ForkOutlinedSvg
  }));
};

/**![fork](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc1MiAxMDBjLTYxLjggMC0xMTIgNTAuMi0xMTIgMTEyIDAgNDcuNyAyOS45IDg4LjUgNzIgMTA0LjZ2MjcuNkw1MTIgNjAxLjQgMzEyIDM0NC4ydi0yNy42YzQyLjEtMTYuMSA3Mi01Ni45IDcyLTEwNC42IDAtNjEuOC01MC4yLTExMi0xMTItMTEycy0xMTIgNTAuMi0xMTIgMTEyYzAgNTAuNiAzMy44IDkzLjUgODAgMTA3LjN2MzQuNGMwIDkuNyAzLjMgMTkuMyA5LjMgMjdMNDc2IDY3Mi4zdjMzLjZjLTQ0LjIgMTUtNzYgNTYuOS03NiAxMDYuMSAwIDYxLjggNTAuMiAxMTIgMTEyIDExMnMxMTItNTAuMiAxMTItMTEyYzAtNDkuMi0zMS44LTkxLTc2LTEwNi4xdi0zMy42bDIyNi43LTI5MS42YzYtNy43IDkuMy0xNy4zIDkuMy0yN3YtMzQuNGM0Ni4yLTEzLjggODAtNTYuNyA4MC0xMDcuMyAwLTYxLjgtNTAuMi0xMTItMTEyLTExMnpNMjI0IDIxMmE0OC4wMSA0OC4wMSAwIDAxOTYgMCA0OC4wMSA0OC4wMSAwIDAxLTk2IDB6bTMzNiA2MDBhNDguMDEgNDguMDEgMCAwMS05NiAwIDQ4LjAxIDQ4LjAxIDAgMDE5NiAwem0xOTItNTUyYTQ4LjAxIDQ4LjAxIDAgMDEwLTk2IDQ4LjAxIDQ4LjAxIDAgMDEwIDk2eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ForkOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ForkOutlined';
}
export default RefIcon;