import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FormatPainterFilledSvg from "@ant-design/icons-svg/es/asn/FormatPainterFilled";
import AntdIcon from "../components/AntdIcon";
var FormatPainterFilled = function FormatPainterFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FormatPainterFilledSvg
  }));
};

/**![format-painter](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NDAgMTkyaC01NnYtNzJjMC0xMy4zLTEwLjctMjQtMjQtMjRIMTY4Yy0xMy4zIDAtMjQgMTAuNy0yNCAyNHYyNzJjMCAxMy4zIDEwLjcgMjQgMjQgMjRoNTkyYzEzLjMgMCAyNC0xMC43IDI0LTI0VjI1NmgzMnYyMDBINDY1Yy0yMi4xIDAtNDAgMTcuOS00MCA0MHYxMzZoLTQ0Yy00LjQgMC04IDMuNi04IDh2MjI4YzAgMS4xLjIgMi4yLjYgMy4xLS40IDEuNi0uNiAzLjItLjYgNC45IDAgNDYuNCAzNy42IDg0IDg0IDg0czg0LTM3LjYgODQtODRjMC0xLjctLjItMy4zLS42LTQuOS40LTEgLjYtMiAuNi0zLjFWNjQwYzAtNC40LTMuNi04LTgtOGgtNDRWNTIwaDM1MWMyMi4xIDAgNDAtMTcuOSA0MC00MFYyMzJjMC0yMi4xLTE3LjktNDAtNDAtNDB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(FormatPainterFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FormatPainterFilled';
}
export default RefIcon;