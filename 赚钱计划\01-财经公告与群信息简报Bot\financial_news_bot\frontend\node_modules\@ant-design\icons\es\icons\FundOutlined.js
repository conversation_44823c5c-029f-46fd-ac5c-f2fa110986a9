import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import FundOutlinedSvg from "@ant-design/icons-svg/es/asn/FundOutlined";
import AntdIcon from "../components/AntdIcon";
var FundOutlined = function FundOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: FundOutlinedSvg
  }));
};

/**![fund](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNiAxNjRIOTRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgNjMySDEzNFYyMzZoNzUydjU2MHptLTY1OC45LTgyLjNjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGwxNzIuNS0xNzIuNSAxMTQuNCAxMTQuNWMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDI5Ny0yOTcuMmMzLjEtMy4xIDMuMS04LjIgMC0xMS4zbC0zNi44LTM2LjhhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDUzMSA1NjUgNDE2LjYgNDUwLjVhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC0yMTQuOSAyMTVhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzYuNyAzNi45eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FundOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FundOutlined';
}
export default RefIcon;