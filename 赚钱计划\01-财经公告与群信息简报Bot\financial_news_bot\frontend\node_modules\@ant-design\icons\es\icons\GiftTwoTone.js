import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import GiftTwoToneSvg from "@ant-design/icons-svg/es/asn/GiftTwoTone";
import AntdIcon from "../components/AntdIcon";
var GiftTwoTone = function GiftTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: GiftTwoToneSvg
  }));
};

/**![gift](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU0NiAzNzhoMjk4djEwNEg1NDZ6TTIyOCA1NTBoMjUwdjMwOEgyMjh6bS00OC0xNzJoMjk4djEwNEgxODB6bTM2NiAxNzJoMjUwdjMwOEg1NDZ6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik04ODAgMzEwSDczMi40YzEzLjYtMjEuNCAyMS42LTQ2LjggMjEuNi03NCAwLTc2LjEtNjEuOS0xMzgtMTM4LTEzOC00MS40IDAtNzguNyAxOC40LTEwNCA0Ny40LTI1LjMtMjktNjIuNi00Ny40LTEwNC00Ny40LTc2LjEgMC0xMzggNjEuOS0xMzggMTM4IDAgMjcuMiA3LjkgNTIuNiAyMS42IDc0SDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjAwYzAgNC40IDMuNiA4IDggOGg0MHYzNDRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjU1MGg0MGM0LjQgMCA4LTMuNiA4LThWMzQyYzAtMTcuNy0xNC4zLTMyLTMyLTMyek00NzggODU4SDIyOFY1NTBoMjUwdjMwOHptMC0zNzZIMTgwVjM3OGgyOTh2MTA0em0wLTE3NmgtNzBjLTM4LjYgMC03MC0zMS40LTcwLTcwczMxLjQtNzAgNzAtNzAgNzAgMzEuNCA3MCA3MHY3MHptNjgtNzBjMC0zOC42IDMxLjQtNzAgNzAtNzBzNzAgMzEuNCA3MCA3MC0zMS40IDcwLTcwIDcwaC03MHYtNzB6bTI1MCA2MjJINTQ2VjU1MGgyNTB2MzA4em00OC0zNzZINTQ2VjM3OGgyOTh2MTA0eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(GiftTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GiftTwoTone';
}
export default RefIcon;