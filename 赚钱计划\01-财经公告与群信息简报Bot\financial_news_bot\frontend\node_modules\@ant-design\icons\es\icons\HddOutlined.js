import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import HddOutlinedSvg from "@ant-design/icons-svg/es/asn/HddOutlined";
import AntdIcon from "../components/AntdIcon";
var HddOutlined = function HddOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: HddOutlinedSvg
  }));
};

/**![hdd](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6bS02MDAgNzJoNTYwdjIwOEgyMzJWMTM2em01NjAgNDgwSDIzMlY0MDhoNTYwdjIwOHptMCAyNzJIMjMyVjY4MGg1NjB2MjA4ek00OTYgMjA4SDMxMmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgxODRjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6TTMxMiA1NDRoMTg0YzQuNCAwIDgtMy42IDgtOHYtNDhjMC00LjQtMy42LTgtOC04SDMxMmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOHptMzI4IDI0NGE0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(HddOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HddOutlined';
}
export default RefIcon;