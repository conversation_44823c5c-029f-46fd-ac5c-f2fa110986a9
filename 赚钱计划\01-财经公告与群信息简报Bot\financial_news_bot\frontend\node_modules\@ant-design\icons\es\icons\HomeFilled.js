import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import HomeFilledSvg from "@ant-design/icons-svg/es/asn/HomeFilled";
import AntdIcon from "../components/AntdIcon";
var HomeFilled = function HomeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: HomeFilledSvg
  }));
};

/**![home](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk0Ni41IDUwNUw1MzQuNiA5My40YTMxLjkzIDMxLjkzIDAgMDAtNDUuMiAwTDc3LjUgNTA1Yy0xMiAxMi0xOC44IDI4LjMtMTguOCA0NS4zIDAgMzUuMyAyOC43IDY0IDY0IDY0aDQzLjRWOTA4YzAgMTcuNyAxNC4zIDMyIDMyIDMySDQ0OFY3MTZoMTEydjIyNGgyNjUuOWMxNy43IDAgMzItMTQuMyAzMi0zMlY2MTQuM2g0My40YzE3IDAgMzMuMy02LjcgNDUuMy0xOC44IDI0LjktMjUgMjQuOS02NS41LS4xLTkwLjV6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(HomeFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HomeFilled';
}
export default RefIcon;