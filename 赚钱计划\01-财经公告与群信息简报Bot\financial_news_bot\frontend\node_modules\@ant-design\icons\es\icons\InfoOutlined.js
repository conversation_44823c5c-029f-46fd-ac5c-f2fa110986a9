import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import InfoOutlinedSvg from "@ant-design/icons-svg/es/asn/InfoOutlined";
import AntdIcon from "../components/AntdIcon";
var InfoOutlined = function InfoOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: InfoOutlinedSvg
  }));
};

/**![info](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ0OCAyMjRhNjQgNjQgMCAxMDEyOCAwIDY0IDY0IDAgMTAtMTI4IDB6bTk2IDE2OGgtNjRjLTQuNCAwLTggMy42LTggOHY0NjRjMCA0LjQgMy42IDggOCA4aDY0YzQuNCAwIDgtMy42IDgtOFY0MDBjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(InfoOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'InfoOutlined';
}
export default RefIcon;