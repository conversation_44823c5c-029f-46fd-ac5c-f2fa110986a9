import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import LockFilledSvg from "@ant-design/icons-svg/es/asn/LockFilled";
import AntdIcon from "../components/AntdIcon";
var LockFilled = function LockFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: LockFilledSvg
  }));
};

/**![lock](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA0NjRoLTY4VjI0MGMwLTcwLjctNTcuMy0xMjgtMTI4LTEyOEgzODhjLTcwLjcgMC0xMjggNTcuMy0xMjggMTI4djIyNGgtNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjM4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNDk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyek01NDAgNzAxdjUzYzAgNC40LTMuNiA4LTggOGgtNDBjLTQuNCAwLTgtMy42LTgtOHYtNTNhNDguMDEgNDguMDEgMCAxMTU2IDB6bTE1Mi0yMzdIMzMyVjI0MGMwLTMwLjkgMjUuMS01NiA1Ni01NmgyNDhjMzAuOSAwIDU2IDI1LjEgNTYgNTZ2MjI0eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(LockFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'LockFilled';
}
export default RefIcon;