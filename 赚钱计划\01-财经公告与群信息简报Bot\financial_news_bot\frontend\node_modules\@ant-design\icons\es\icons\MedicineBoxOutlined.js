import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MedicineBoxOutlinedSvg from "@ant-design/icons-svg/es/asn/MedicineBoxOutlined";
import AntdIcon from "../components/AntdIcon";
var MedicineBoxOutlined = function MedicineBoxOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MedicineBoxOutlinedSvg
  }));
};

/**![medicine-box](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzOS4yIDI3OC4xYTMyIDMyIDAgMDAtMzAuNC0yMi4xSDczNlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMzIwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMTJoLTcyLjhhMzEuOSAzMS45IDAgMDAtMzAuNCAyMi4xTDExMiA1MDJ2Mzc4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlY1MDJsLTcyLjgtMjIzLjl6TTM2MCAxODRoMzA0djcySDM2MHYtNzJ6bTQ4MCA2NTZIMTg0VjUxMy40TDI0NC4zIDMyOGg1MzUuNEw4NDAgNTEzLjRWODQwek02NTIgNTcySDU0NFY0NjRjMC00LjQtMy42LTgtOC04aC00OGMtNC40IDAtOCAzLjYtOCA4djEwOEgzNzJjLTQuNCAwLTggMy42LTggOHY0OGMwIDQuNCAzLjYgOCA4IDhoMTA4djEwOGMwIDQuNCAzLjYgOCA4IDhoNDhjNC40IDAgOC0zLjYgOC04VjYzNmgxMDhjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MedicineBoxOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MedicineBoxOutlined';
}
export default RefIcon;