import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MobileTwoToneSvg from "@ant-design/icons-svg/es/asn/MobileTwoTone";
import AntdIcon from "../components/AntdIcon";
var MobileTwoTone = function MobileTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MobileTwoToneSvg
  }));
};

/**![mobile](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc0NCA2NEgyODBjLTM1LjMgMC02NCAyOC43LTY0IDY0djc2OGMwIDM1LjMgMjguNyA2NCA2NCA2NGg0NjRjMzUuMyAwIDY0LTI4LjcgNjQtNjRWMTI4YzAtMzUuMy0yOC43LTY0LTY0LTY0em0tOCA4MjRIMjg4VjEzNmg0NDh2NzUyeiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNMjg4IDg4OGg0NDhWMTM2SDI4OHY3NTJ6bTIyNC0xNDJjMjIuMSAwIDQwIDE3LjkgNDAgNDBzLTE3LjkgNDAtNDAgNDAtNDAtMTcuOS00MC00MCAxNy45LTQwIDQwLTQweiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNDcyIDc4NmE0MCA0MCAwIDEwODAgMCA0MCA0MCAwIDEwLTgwIDB6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(MobileTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MobileTwoTone';
}
export default RefIcon;