import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import MoonFilledSvg from "@ant-design/icons-svg/es/asn/MoonFilled";
import AntdIcon from "../components/AntdIcon";
var MoonFilled = function MoonFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: MoonFilledSvg
  }));
};

/**![moon](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNDg5LjUgMTExLjY2YzMwLjY1LTEuOCA0NS45OCAzNi40NCAyMi41OCA1Ni4zM0EyNDMuMzUgMjQzLjM1IDAgMDA0MjYgMzU0YzAgMTM0Ljc2IDEwOS4yNCAyNDQgMjQ0IDI0NCA3Mi41OCAwIDEzOS45LTMxLjgzIDE4Ni4wMS04Ni4wOCAxOS44Ny0yMy4zOCA1OC4wNy04LjEgNTYuMzQgMjIuNTNDOTAwLjQgNzQ1LjgyIDcyNS4xNSA5MTIgNTEyLjUgOTEyIDI5MS4zMSA5MTIgMTEyIDczMi42OSAxMTIgNTExLjVjMC0yMTEuMzkgMTY0LjI5LTM4Ni4wMiAzNzQuMi0zOTkuNjVsLjItLjAxeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(MoonFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'MoonFilled';
}
export default RefIcon;