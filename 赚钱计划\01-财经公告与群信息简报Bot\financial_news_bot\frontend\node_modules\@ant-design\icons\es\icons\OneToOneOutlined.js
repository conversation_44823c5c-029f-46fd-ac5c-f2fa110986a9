import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import OneToOneOutlinedSvg from "@ant-design/icons-svg/es/asn/OneToOneOutlined";
import AntdIcon from "../components/AntdIcon";
var OneToOneOutlined = function OneToOneOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: OneToOneOutlinedSvg
  }));
};

/**![one-to-one](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMTYgNjcyaDYwYzQuNCAwIDgtMy42IDgtOFYzNjBjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4djMwNGMwIDQuNCAzLjYgOCA4IDh6bTE5Ni01MGMyMi4xIDAgNDAtMTcuOSA0MC0zOSAwLTIzLjEtMTcuOS00MS00MC00MXMtNDAgMTcuOS00MCA0MWMwIDIxLjEgMTcuOSAzOSA0MCAzOXptMC0xNDBjMjIuMSAwIDQwLTE3LjkgNDAtMzkgMC0yMy4xLTE3LjktNDEtNDAtNDFzLTQwIDE3LjktNDAgNDFjMCAyMS4xIDE3LjkgMzkgNDAgMzl6IiAvPjxwYXRoIGQ9Ik04ODAgMTEySDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJ6bS00MCA3MjhIMTg0VjE4NGg2NTZ2NjU2eiIgLz48cGF0aCBkPSJNNjQ4IDY3Mmg2MGM0LjQgMCA4LTMuNiA4LThWMzYwYzAtNC40LTMuNi04LTgtOGgtNjBjLTQuNCAwLTggMy42LTggOHYzMDRjMCA0LjQgMy42IDggOCA4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(OneToOneOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'OneToOneOutlined';
}
export default RefIcon;