import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PlayCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/PlayCircleTwoTone";
import AntdIcon from "../components/AntdIcon";
var PlayCircleTwoTone = function PlayCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PlayCircleTwoToneSvg
  }));
};

/**![play-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTE2NC4xIDM3OC4yTDQ1Ny43IDY3Ny4xYTguMDIgOC4wMiAwIDAxLTEyLjctNi41VjM1M2E4IDggMCAwMTEyLjctNi41bDIxOC40IDE1OC44YTcuOSA3LjkgMCAwMTAgMTIuOXoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTY3Ni4xIDUwNS4zTDQ1Ny43IDM0Ni41QTggOCAwIDAwNDQ1IDM1M3YzMTcuNmE4LjAyIDguMDIgMCAwMDEyLjcgNi41bDIxOC40LTE1OC45YTcuOSA3LjkgMCAwMDAtMTIuOXoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PlayCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PlayCircleTwoTone';
}
export default RefIcon;