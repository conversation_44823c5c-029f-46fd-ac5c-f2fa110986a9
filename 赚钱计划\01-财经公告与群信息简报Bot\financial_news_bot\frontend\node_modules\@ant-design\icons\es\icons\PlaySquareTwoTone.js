import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PlaySquareTwoToneSvg from "@ant-design/icons-svg/es/asn/PlaySquareTwoTone";
import AntdIcon from "../components/AntdIcon";
var PlaySquareTwoTone = function PlaySquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: PlaySquareTwoToneSvg
  }));
};

/**![play-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMjQwLTQ4NC43YzAtOS40IDEwLjktMTQuNyAxOC4zLTguOGwxOTkuNCAxNTYuN2ExMS4yIDExLjIgMCAwMTAgMTcuNkw0NDIuMyA2NzcuNmMtNy40IDUuOC0xOC4zLjYtMTguMy04LjhWMzU1LjN6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik00NDIuMyA2NzcuNmwxOTkuNC0xNTYuOGExMS4yIDExLjIgMCAwMDAtMTcuNkw0NDIuMyAzNDYuNWMtNy40LTUuOS0xOC4zLS42LTE4LjMgOC44djMxMy41YzAgOS40IDEwLjkgMTQuNiAxOC4zIDguOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PlaySquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PlaySquareTwoTone';
}
export default RefIcon;