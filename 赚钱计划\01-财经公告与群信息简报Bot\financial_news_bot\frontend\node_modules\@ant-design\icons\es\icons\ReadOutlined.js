import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ReadOutlinedSvg from "@ant-design/icons-svg/es/asn/ReadOutlined";
import AntdIcon from "../components/AntdIcon";
var ReadOutlined = function ReadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ReadOutlinedSvg
  }));
};

/**![read](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAxNjFINjk5LjJjLTQ5LjEgMC05Ny4xIDE0LjEtMTM4LjQgNDAuN0w1MTIgMjMzbC00OC44LTMxLjNBMjU1LjIgMjU1LjIgMCAwMDMyNC44IDE2MUg5NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NTY4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDIyOC44YzQ5LjEgMCA5Ny4xIDE0LjEgMTM4LjQgNDAuN2w0NC40IDI4LjZjMS4zLjggMi44IDEuMyA0LjMgMS4zczMtLjQgNC4zLTEuM2w0NC40LTI4LjZDNjAyIDgwNy4xIDY1MC4xIDc5MyA2OTkuMiA3OTNIOTI4YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE5M2MwLTE3LjctMTQuMy0zMi0zMi0zMnpNMzI0LjggNzIxSDEzNlYyMzNoMTg4LjhjMzUuNCAwIDY5LjggMTAuMSA5OS41IDI5LjJsNDguOCAzMS4zIDYuOSA0LjV2NDYyYy00Ny42LTI1LjYtMTAwLjgtMzktMTU1LjItMzl6bTU2My4yIDBINjk5LjJjLTU0LjQgMC0xMDcuNiAxMy40LTE1NS4yIDM5VjI5OGw2LjktNC41IDQ4LjgtMzEuM2MyOS43LTE5LjEgNjQuMS0yOS4yIDk5LjUtMjkuMkg4ODh2NDg4ek0zOTYuOSAzNjFIMjExLjFjLTMuOSAwLTcuMSAzLjQtNy4xIDcuNXY0NWMwIDQuMSAzLjIgNy41IDcuMSA3LjVoMTg1LjdjMy45IDAgNy4xLTMuNCA3LjEtNy41di00NWMuMS00LjEtMy4xLTcuNS03LTcuNXptMjIzLjEgNy41djQ1YzAgNC4xIDMuMiA3LjUgNy4xIDcuNWgxODUuN2MzLjkgMCA3LjEtMy40IDcuMS03LjV2LTQ1YzAtNC4xLTMuMi03LjUtNy4xLTcuNUg2MjcuMWMtMy45IDAtNy4xIDMuNC03LjEgNy41ek0zOTYuOSA1MDFIMjExLjFjLTMuOSAwLTcuMSAzLjQtNy4xIDcuNXY0NWMwIDQuMSAzLjIgNy41IDcuMSA3LjVoMTg1LjdjMy45IDAgNy4xLTMuNCA3LjEtNy41di00NWMuMS00LjEtMy4xLTcuNS03LTcuNXptNDE2IDBINjI3LjFjLTMuOSAwLTcuMSAzLjQtNy4xIDcuNXY0NWMwIDQuMSAzLjIgNy41IDcuMSA3LjVoMTg1LjdjMy45IDAgNy4xLTMuNCA3LjEtNy41di00NWMuMS00LjEtMy4xLTcuNS03LTcuNXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(ReadOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ReadOutlined';
}
export default RefIcon;