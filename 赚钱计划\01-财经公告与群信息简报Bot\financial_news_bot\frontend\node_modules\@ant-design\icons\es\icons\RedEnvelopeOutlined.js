import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RedEnvelopeOutlinedSvg from "@ant-design/icons-svg/es/asn/RedEnvelopeOutlined";
import AntdIcon from "../components/AntdIcon";
var RedEnvelopeOutlined = function RedEnvelopeOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RedEnvelopeOutlinedSvg
  }));
};

/**![red-envelope](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ0MC42IDQ2Mi42YTguMzggOC4zOCAwIDAwLTcuNS00LjZoLTQ4LjhjLTEuMyAwLTIuNi40LTMuOSAxYTguNCA4LjQgMCAwMC0zLjQgMTEuNGw4Ny40IDE2MS4xSDQxOWMtNC42IDAtOC40IDMuOC04LjQgOC40VjY2NWMwIDQuNiAzLjggOC40IDguNCA4LjRoNjNWNzAyaC02M2MtNC42IDAtOC40IDMuOC04LjQgOC40djI1LjFjMCA0LjYgMy44IDguNCA4LjQgOC40aDYzdjQ5LjljMCA0LjYgMy44IDguNCA4LjQgOC40aDQzLjdjNC42IDAgOC40LTMuOCA4LjQtOC40di00OS45aDYzLjNjNC43IDAgOC40LTMuOCA4LjItOC41di0yNWMwLTQuNi0zLjgtOC40LTguNC04LjRoLTYzLjN2LTI4LjZoNjMuM2M0LjYgMCA4LjQtMy44IDguNC04LjR2LTI1LjFjMC00LjYtMy44LTguNC04LjQtOC40aC00NS45bDg3LjItMTYxYTguNDUgOC40NSAwIDAwLTcuNC0xMi40aC00Ny44Yy0zLjEgMC02IDEuOC03LjUgNC42bC03MS45IDE0MS45LTcxLjctMTQyek04MzIgNjRIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY4MzJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgODI0SDIzMlYxOTMuMWwyNjAuMyAyMDQuMWMxMS42IDkuMSAyNy45IDkuMSAzOS41IDBMNzkyIDE5My4xVjg4OHptMC03NTEuM2gtMzEuN0w1MTIgMzMxLjMgMjYzLjcgMTM2LjdIMjMydi0uN2g1NjB2Ljd6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(RedEnvelopeOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RedEnvelopeOutlined';
}
export default RefIcon;