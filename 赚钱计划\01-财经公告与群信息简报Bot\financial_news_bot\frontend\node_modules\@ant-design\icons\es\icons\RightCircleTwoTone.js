import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RightCircleTwoToneSvg from "@ant-design/icons-svg/es/asn/RightCircleTwoTone";
import AntdIcon from "../components/AntdIcon";
var RightCircleTwoTone = function RightCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RightCircleTwoToneSvg
  }));
};

/**![right-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTE1NC43IDM3OC40bC0yNDYgMTc4Yy01LjMgMy44LTEyLjcgMC0xMi43LTYuNVY2NDNjMC0xMC4yIDQuOS0xOS45IDEzLjItMjUuOUw1NjYuNiA1MTIgNDIxLjIgNDA2LjhjLTguMy02LTEzLjItMTUuNi0xMy4yLTI1LjlWMzM0YzAtNi41IDcuNC0xMC4zIDEyLjctNi41bDI0NiAxNzhjNC40IDMuMiA0LjQgOS43IDAgMTIuOXoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTY2Ni43IDUwNS41bC0yNDYtMTc4Yy01LjMtMy44LTEyLjcgMC0xMi43IDYuNXY0Ni45YzAgMTAuMyA0LjkgMTkuOSAxMy4yIDI1LjlMNTY2LjYgNTEyIDQyMS4yIDYxNy4xYy04LjMgNi0xMy4yIDE1LjctMTMuMiAyNS45djQ2LjljMCA2LjUgNy40IDEwLjMgMTIuNyA2LjVsMjQ2LTE3OGM0LjQtMy4yIDQuNC05LjcgMC0xMi45eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(RightCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RightCircleTwoTone';
}
export default RefIcon;