import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ScanOutlinedSvg from "@ant-design/icons-svg/es/asn/ScanOutlined";
import AntdIcon from "../components/AntdIcon";
var ScanOutlined = function ScanOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ScanOutlinedSvg
  }));
};

/**![scan](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEzNiAzODRoNTZjNC40IDAgOC0zLjYgOC04VjIwMGgxNzZjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LThIMTk2Yy0zNy42IDAtNjggMzAuNC02OCA2OHYxODBjMCA0LjQgMy42IDggOCA4em01MTItMTg0aDE3NnYxNzZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYxOTZjMC0zNy42LTMwLjQtNjgtNjgtNjhINjQ4Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4ek0zNzYgODI0SDIwMFY2NDhjMC00LjQtMy42LTgtOC04aC01NmMtNC40IDAtOCAzLjYtOCA4djE4MGMwIDM3LjYgMzAuNCA2OCA2OCA2OGgxODBjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTUxMi0xODRoLTU2Yy00LjQgMC04IDMuNi04IDh2MTc2SDY0OGMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGgxODBjMzcuNiAwIDY4LTMwLjQgNjgtNjhWNjQ4YzAtNC40LTMuNi04LTgtOHptMTYtMTY0SDEyMGMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg3ODRjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(ScanOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ScanOutlined';
}
export default RefIcon;