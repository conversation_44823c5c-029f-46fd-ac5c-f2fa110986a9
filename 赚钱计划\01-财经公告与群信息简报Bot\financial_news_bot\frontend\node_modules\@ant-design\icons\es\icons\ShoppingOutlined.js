import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ShoppingOutlinedSvg from "@ant-design/icons-svg/es/asn/ShoppingOutlined";
import AntdIcon from "../components/AntdIcon";
var ShoppingOutlined = function ShoppingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ShoppingOutlinedSvg
  }));
};

/**![shopping](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAzMTJINjk2di0xNmMwLTEwMS42LTgyLjQtMTg0LTE4NC0xODRzLTE4NCA4Mi40LTE4NCAxODR2MTZIMTkyYy0xNy43IDAtMzIgMTQuMy0zMiAzMnY1MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNjQwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjM0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQzMi0xNmMwLTYxLjkgNTAuMS0xMTIgMTEyLTExMnMxMTIgNTAuMSAxMTIgMTEydjE2SDQwMHYtMTZ6bTM5MiA1NDRIMjMyVjM4NGg5NnY4OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di04OGgyMjR2ODhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtODhoOTZ2NDU2eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ShoppingOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ShoppingOutlined';
}
export default RefIcon;