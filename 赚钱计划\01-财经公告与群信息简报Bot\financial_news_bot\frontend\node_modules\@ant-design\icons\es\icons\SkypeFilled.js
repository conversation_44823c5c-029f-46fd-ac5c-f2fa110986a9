import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import SkypeFilledSvg from "@ant-design/icons-svg/es/asn/SkypeFilled";
import AntdIcon from "../components/AntdIcon";
var SkypeFilled = function SkypeFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: SkypeFilledSvg
  }));
};

/**![skype](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4My43IDU3OC42YzQuMS0yMi41IDYuMy00NS41IDYuMy02OC41IDAtNTEtMTAtMTAwLjUtMjkuNy0xNDctMTktNDUtNDYuMy04NS40LTgxLTEyMC4xYTM3NS43OSAzNzUuNzkgMCAwMC0xMjAuMS04MC45Yy00Ni42LTE5LjctOTYtMjkuNy0xNDctMjkuNy0yNCAwLTQ4LjEgMi4zLTcxLjUgNi44QTIyNS4xIDIyNS4xIDAgMDAzMzUuNiAxMTNjLTU5LjcgMC0xMTUuOSAyMy4zLTE1OC4xIDY1LjVBMjIyLjI1IDIyMi4yNSAwIDAwMTEyIDMzNi42YzAgMzggOS44IDc1LjQgMjguMSAxMDguNC0zLjcgMjEuNC01LjcgNDMuMy01LjcgNjUuMSAwIDUxIDEwIDEwMC41IDI5LjcgMTQ3IDE5IDQ1IDQ2LjIgODUuNCA4MC45IDEyMC4xIDM0LjcgMzQuNyA3NS4xIDYxLjkgMTIwLjEgODAuOSA0Ni42IDE5LjcgOTYgMjkuNyAxNDcgMjkuNyAyMi4yIDAgNDQuNC0yIDY2LjItNS45IDMzLjUgMTguOSA3MS4zIDI5IDExMCAyOSA1OS43IDAgMTE1LjktMjMuMiAxNTguMS02NS41IDQyLjMtNDIuMiA2NS41LTk4LjQgNjUuNS0xNTguMS4xLTM4LTkuNy03NS41LTI4LjItMTA4Ljd6bS0zNzAgMTYyLjljLTEzNC4yIDAtMTk0LjItNjYtMTk0LjItMTE1LjQgMC0yNS40IDE4LjctNDMuMSA0NC41LTQzLjEgNTcuNCAwIDQyLjYgODIuNSAxNDkuNyA4Mi41IDU0LjkgMCA4NS4yLTI5LjggODUuMi02MC4zIDAtMTguMy05LTM4LjctNDUuMi00Ny42bC0xMTkuNC0yOS44Yy05Ni4xLTI0LjEtMTEzLjYtNzYuMS0xMTMuNi0xMjQuOSAwLTEwMS40IDk1LjUtMTM5LjUgMTg1LjItMTM5LjUgODIuNiAwIDE4MCA0NS43IDE4MCAxMDYuNSAwIDI2LjEtMjIuNiA0MS4yLTQ4LjQgNDEuMi00OSAwLTQwLTY3LjgtMTM4LjctNjcuOC00OSAwLTc2LjEgMjIuMi03Ni4xIDUzLjlzMzguNyA0MS44IDcyLjMgNDkuNWw4OC40IDE5LjZjOTYuOCAyMS42IDEyMS4zIDc4LjEgMTIxLjMgMTMxLjMgMCA4Mi4zLTYzLjMgMTQzLjktMTkxIDE0My45eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(SkypeFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'SkypeFilled';
}
export default RefIcon;