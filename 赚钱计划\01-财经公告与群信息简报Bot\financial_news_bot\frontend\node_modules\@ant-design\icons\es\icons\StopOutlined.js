import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import StopOutlinedSvg from "@ant-design/icons-svg/es/asn/StopOutlined";
import AntdIcon from "../components/AntdIcon";
var StopOutlined = function StopOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: StopOutlinedSvg
  }));
};

/**![stop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MiAwLTg5IDMxLjMtMTcwLjggODMuNS0yMzQuOGw1MjMuMyA1MjMuM0M2ODIuOCA4NTIuNyA2MDEgODg0IDUxMiA4ODR6bTI4OC41LTEzNy4yTDI3Ny4yIDIyMy41QzM0MS4yIDE3MS4zIDQyMyAxNDAgNTEyIDE0MGMyMDUuNCAwIDM3MiAxNjYuNiAzNzIgMzcyIDAgODktMzEuMyAxNzAuOC04My41IDIzNC44eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(StopOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'StopOutlined';
}
export default RefIcon;