import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import TagTwoToneSvg from "@ant-design/icons-svg/es/asn/TagTwoTone";
import AntdIcon from "../components/AntdIcon";
var TagTwoTone = function TagTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: TagTwoToneSvg
  }));
};

/**![tag](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU4OSAxNjQuNkwxODkuMyA1NjQuM2wyNzAuNCAyNzAuNEw4NTkuNCA0MzUgODM2IDE4OGwtMjQ3LTIzLjR6TTY4MCA0MzJjLTQ4LjUgMC04OC0zOS41LTg4LTg4czM5LjUtODggODgtODggODggMzkuNSA4OCA4OC0zOS41IDg4LTg4IDg4eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNjgwIDI1NmMtNDguNSAwLTg4IDM5LjUtODggODhzMzkuNSA4OCA4OCA4OCA4OC0zOS41IDg4LTg4LTM5LjUtODgtODgtODh6bTAgMTIwYy0xNy43IDAtMzItMTQuMy0zMi0zMnMxNC4zLTMyIDMyLTMyIDMyIDE0LjMgMzIgMzItMTQuMyAzMi0zMiAzMnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTkzOCA0NTguOGwtMjkuNi0zMTIuNmMtMS41LTE2LjItMTQuNC0yOS0zMC42LTMwLjZMNTY1LjIgODZoLS40Yy0zLjIgMC01LjcgMS03LjYgMi45TDg4LjkgNTU3LjJhOS45NiA5Ljk2IDAgMDAwIDE0LjFsMzYzLjggMzYzLjhhOS45IDkuOSAwIDAwNy4xIDIuOWMyLjcgMCA1LjItMSA3LjEtMi45bDQ2OC4zLTQ2OC4zYzItMi4xIDMtNSAyLjgtOHpNNDU5LjcgODM0LjdMMTg5LjMgNTY0LjMgNTg5IDE2NC42IDgzNiAxODhsMjMuNCAyNDctMzk5LjcgMzk5Ljd6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(TagTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'TagTwoTone';
}
export default RefIcon;