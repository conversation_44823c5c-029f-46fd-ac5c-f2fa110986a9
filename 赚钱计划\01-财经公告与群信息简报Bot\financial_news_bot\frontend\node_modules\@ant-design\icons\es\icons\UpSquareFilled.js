import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import UpSquareFilledSvg from "@ant-design/icons-svg/es/asn/UpSquareFilled";
import AntdIcon from "../components/AntdIcon";
var UpSquareFilled = function UpSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: UpSquareFilledSvg
  }));
};

/**![up-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjkwIDYyNGgtNDYuOWMtMTAuMiAwLTE5LjktNC45LTI1LjktMTMuMkw1MTIgNDY1LjQgNDA2LjggNjEwLjhjLTYgOC4zLTE1LjYgMTMuMi0yNS45IDEzLjJIMzM0Yy02LjUgMC0xMC4zLTcuNC02LjUtMTIuN2wxNzgtMjQ2YzMuMi00LjQgOS43LTQuNCAxMi45IDBsMTc4IDI0NmMzLjkgNS4zLjEgMTIuNy02LjQgMTIuN3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(UpSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'UpSquareFilled';
}
export default RefIcon;