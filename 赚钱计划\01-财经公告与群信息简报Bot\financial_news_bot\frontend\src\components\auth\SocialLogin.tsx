import React, { useState } from 'react';
import { Button, Space, message, Modal, Typography } from 'antd';
import {
  WechatOutlined,
  QqOutlined,
  AlipayOutlined,
  GithubOutlined,
  GoogleOutlined,
} from '@ant-design/icons';

import { useAppDispatch } from '../../store';
import { socialLogin } from '../../store/slices/authSlice';

const { Text } = Typography;

interface SocialLoginProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

interface SocialProvider {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  enabled: boolean;
}

const SocialLogin: React.FC<SocialLoginProps> = ({ onSuccess, onError }) => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<string | null>(null);
  const [qrCodeVisible, setQrCodeVisible] = useState(false);
  const [currentProvider, setCurrentProvider] = useState<string>('');

  const providers: SocialProvider[] = [
    {
      id: 'wechat',
      name: '微信',
      icon: <WechatOutlined />,
      color: '#07c160',
      enabled: true,
    },
    {
      id: 'qq',
      name: 'QQ',
      icon: <QqOutlined />,
      color: '#1296db',
      enabled: true,
    },
    {
      id: 'alipay',
      name: '支付宝',
      icon: <AlipayOutlined />,
      color: '#1677ff',
      enabled: false, // 可选功能
    },
    {
      id: 'github',
      name: 'GitHub',
      icon: <GithubOutlined />,
      color: '#24292e',
      enabled: true,
    },
    {
      id: 'google',
      name: 'Google',
      icon: <GoogleOutlined />,
      color: '#4285f4',
      enabled: true,
    },
  ];

  const handleSocialLogin = async (providerId: string) => {
    setLoading(providerId);
    setCurrentProvider(providerId);

    try {
      // 微信登录需要显示二维码
      if (providerId === 'wechat') {
        setQrCodeVisible(true);
        // 真实微信登录流程
        await handleWechatLogin();
      } else {
        // 其他第三方登录使用OAuth2.0重定向
        await handleOAuthLogin(providerId);
      }
    } catch (error: any) {
      const errorMessage = error.message || `${getProviderName(providerId)}登录失败`;
      message.error(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(null);
      setQrCodeVisible(false);
    }
  };

  const handleWechatLogin = async () => {
    try {
      // 真实微信登录流程 - 获取二维码
      const response = await fetch('/api/v1/auth/wechat/qrcode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        const qrCodeUrl = data.qr_code_url;
        const ticket = data.ticket;

        // 轮询检查扫码状态
        await pollWechatLoginStatus(ticket);
      } else {
        throw new Error('获取微信二维码失败');
      }
    } catch (error) {
      message.error('微信登录失败，请重试');
      setQrCodeVisible(false);
      throw error;
    }
  };

  const pollWechatLoginStatus = async (ticket: string) => {
    return new Promise<void>((resolve, reject) => {
      let countdown = 120; // 2分钟超时
      const timer = setInterval(async () => {
        countdown--;
        if (countdown <= 0) {
          clearInterval(timer);
          reject(new Error('二维码已过期，请重新获取'));
          return;
        }

        try {
          // 检查扫码状态
          const response = await fetch(`/api/v1/auth/wechat/status/${ticket}`);
          if (response.ok) {
            const data = await response.json();
            if (data.status === 'success') {
              clearInterval(timer);
              await handleLoginSuccess('wechat', data.user_info);
              resolve();
            } else if (data.status === 'expired') {
              clearInterval(timer);
              reject(new Error('二维码已过期'));
            }
          }
        } catch (error) {
          // 继续轮询，不中断
        }
      }, 2000); // 每2秒检查一次
    });
  };

  const handleOAuthLogin = async (providerId: string) => {
    // 构建OAuth2.0授权URL
    const authUrl = buildOAuthUrl(providerId);
    
    // 打开新窗口进行OAuth授权
    const authWindow = window.open(
      authUrl,
      'social_login',
      'width=600,height=600,scrollbars=yes,resizable=yes'
    );

    if (!authWindow) {
      throw new Error('无法打开授权窗口，请检查浏览器弹窗设置');
    }

    // 监听授权结果
    return new Promise<void>((resolve, reject) => {
      const checkClosed = setInterval(() => {
        if (authWindow.closed) {
          clearInterval(checkClosed);
          reject(new Error('用户取消了授权'));
        }
      }, 1000);

      // 监听消息事件
      const messageHandler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'SOCIAL_LOGIN_SUCCESS') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          authWindow.close();
          
          handleLoginSuccess(providerId, event.data.userInfo);
          resolve();
        } else if (event.data.type === 'SOCIAL_LOGIN_ERROR') {
          clearInterval(checkClosed);
          window.removeEventListener('message', messageHandler);
          authWindow.close();
          
          reject(new Error(event.data.error));
        }
      };

      window.addEventListener('message', messageHandler);

      // 真实OAuth授权流程 - 不需要模拟，由回调页面处理
    });
  };

  const buildOAuthUrl = (providerId: string): string => {
    const baseUrls = {
      qq: 'https://graph.qq.com/oauth2.0/authorize',
      github: 'https://github.com/login/oauth/authorize',
      google: 'https://accounts.google.com/oauth2/auth',
      alipay: 'https://openauth.alipay.com/oauth2/publicAppAuthorize.htm',
    };

    const clientIds = {
      qq: process.env.REACT_APP_QQ_CLIENT_ID,
      github: process.env.REACT_APP_GITHUB_CLIENT_ID,
      google: process.env.REACT_APP_GOOGLE_CLIENT_ID,
      alipay: process.env.REACT_APP_ALIPAY_CLIENT_ID,
    };

    // 检查必需的客户端ID
    if (!clientIds[providerId as keyof typeof clientIds]) {
      throw new Error(`${providerId} 客户端ID未配置`);
    }

    const redirectUri = `${window.location.origin}/auth/callback/${providerId}`;
    const state = generateRandomState();

    const clientId = clientIds[providerId as keyof typeof clientIds];
    if (!clientId) {
      throw new Error(`${providerId} 客户端ID未配置`);
    }

    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: getOAuthScope(providerId),
      state: state,
    });

    return `${baseUrls[providerId as keyof typeof baseUrls]}?${params.toString()}`;
  };

  const getOAuthScope = (providerId: string): string => {
    const scopes = {
      qq: 'get_user_info',
      github: 'user:email',
      google: 'openid email profile',
      alipay: 'auth_user',
    };
    return scopes[providerId as keyof typeof scopes] || '';
  };

  const generateRandomState = (): string => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  };

  // 移除模拟用户信息函数 - 使用真实OAuth回调数据

  const handleLoginSuccess = async (providerId: string, userInfo: any) => {
    try {
      // 调用Redux action处理社交登录
      await dispatch(socialLogin({
        provider: providerId,
        userInfo: userInfo,
      })).unwrap();

      message.success(`${getProviderName(providerId)}登录成功`);
      onSuccess?.();
    } catch (error: any) {
      throw new Error(error.message || '登录处理失败');
    }
  };

  const getProviderName = (providerId: string): string => {
    const provider = providers.find(p => p.id === providerId);
    return provider?.name || providerId;
  };

  const renderQrCodeModal = () => (
    <Modal
      title="微信登录"
      open={qrCodeVisible}
      onCancel={() => setQrCodeVisible(false)}
      footer={null}
      width={300}
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <div
          style={{
            width: '200px',
            height: '200px',
            border: '1px solid #d9d9d9',
            margin: '0 auto 16px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#f5f5f5',
          }}
        >
          <div style={{ textAlign: 'center' }}>
            <WechatOutlined style={{ fontSize: '48px', color: '#07c160' }} />
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              二维码加载中...
            </div>
          </div>
        </div>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          请使用微信扫描二维码登录
        </Text>
      </div>
    </Modal>
  );

  return (
    <div>
      <Space wrap>
        {providers
          .filter(provider => provider.enabled)
          .map(provider => (
            <Button
              key={provider.id}
              icon={provider.icon}
              loading={loading === provider.id}
              onClick={() => handleSocialLogin(provider.id)}
              style={{
                borderColor: provider.color,
                color: provider.color,
              }}
              size="large"
            >
              {provider.name}
            </Button>
          ))}
      </Space>
      
      {renderQrCodeModal()}
    </div>
  );
};

export default SocialLogin;
