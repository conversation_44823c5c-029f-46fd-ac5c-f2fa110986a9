import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Button, 
  Form, 
  Input, 
  Rate, 
  Select, 
  Checkbox, 
  Typography, 
  Space, 
  Card,
  message,
  Progress,
  Tag,
} from 'antd';
import {
  SmileOutlined,
  FrownOutlined,
  MehOutlined,
  BugOutlined,
  BulbOutlined,
  HeartOutlined,
  SendOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import useResponsive from '../../hooks/useResponsive';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

interface FeedbackData {
  type: 'bug' | 'suggestion' | 'compliment' | 'usability';
  rating: number;
  category: string;
  title: string;
  description: string;
  steps?: string;
  expectedBehavior?: string;
  actualBehavior?: string;
  browserInfo: string;
  pageUrl: string;
  userAgent: string;
  timestamp: number;
  userId?: string;
  sessionId: string;
  features: string[];
  difficulty: number;
  satisfaction: number;
  recommendation: number;
}

interface FeedbackCollectorProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (feedback: FeedbackData) => void;
  triggerType?: 'manual' | 'auto' | 'exit';
  initialType?: FeedbackData['type'];
}

const FeedbackCollector: React.FC<FeedbackCollectorProps> = ({
  visible,
  onClose,
  onSubmit,
  triggerType = 'manual',
  initialType = 'suggestion',
}) => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [feedbackType, setFeedbackType] = useState<FeedbackData['type']>(initialType);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { isMobile } = useResponsive();

  const feedbackTypes = [
    {
      key: 'bug' as const,
      label: '报告问题',
      icon: <BugOutlined />,
      color: '#ff4d4f',
      description: '发现了系统错误或异常行为',
    },
    {
      key: 'suggestion' as const,
      label: '功能建议',
      icon: <BulbOutlined />,
      color: '#faad14',
      description: '希望添加新功能或改进现有功能',
    },
    {
      key: 'compliment' as const,
      label: '表扬反馈',
      icon: <HeartOutlined />,
      color: '#52c41a',
      description: '分享您的满意体验',
    },
    {
      key: 'usability' as const,
      label: '可用性评价',
      icon: <SmileOutlined />,
      color: '#1890ff',
      description: '评价系统的易用性和用户体验',
    },
  ];

  const categories = {
    bug: ['界面显示', '功能异常', '性能问题', '数据错误', '其他'],
    suggestion: ['新功能', '界面改进', '性能优化', '用户体验', '其他'],
    compliment: ['界面设计', '功能实用', '性能表现', '用户体验', '其他'],
    usability: ['导航体验', '操作流程', '信息架构', '视觉设计', '其他'],
  };

  const steps = [
    { title: '选择类型', description: '选择反馈类型' },
    { title: '详细信息', description: '填写具体内容' },
    { title: '评价体验', description: '评价使用体验' },
  ];

  useEffect(() => {
    if (visible) {
      setCurrentStep(0);
      setFeedbackType(initialType);
      form.resetFields();
    }
  }, [visible, initialType, form]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const values = await form.validateFields();
      
      const feedbackData: FeedbackData = {
        type: feedbackType,
        rating: values.rating || 5,
        category: values.category,
        title: values.title,
        description: values.description,
        steps: values.steps,
        expectedBehavior: values.expectedBehavior,
        actualBehavior: values.actualBehavior,
        browserInfo: navigator.userAgent,
        pageUrl: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        sessionId: generateSessionId(),
        features: values.features || [],
        difficulty: values.difficulty || 3,
        satisfaction: values.satisfaction || 5,
        recommendation: values.recommendation || 5,
      };

      await onSubmit(feedbackData);
      message.success('感谢您的反馈！我们会认真考虑您的建议。');
      onClose();
    } catch (error) {
      message.error('提交失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateSessionId = () => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div>
            <Title level={4} style={{ textAlign: 'center', marginBottom: 24 }}>
              请选择反馈类型
            </Title>
            <div style={{ display: 'grid', gap: 12, gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr' }}>
              {feedbackTypes.map(type => (
                <Card
                  key={type.key}
                  size="small"
                  hoverable
                  onClick={() => setFeedbackType(type.key)}
                  style={{
                    borderColor: feedbackType === type.key ? type.color : '#d9d9d9',
                    backgroundColor: feedbackType === type.key ? `${type.color}10` : 'white',
                  }}
                  bodyStyle={{ padding: 16 }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <span style={{ fontSize: 20, color: type.color, marginRight: 8 }}>
                      {type.icon}
                    </span>
                    <Text strong>{type.label}</Text>
                  </div>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {type.description}
                  </Text>
                </Card>
              ))}
            </div>
          </div>
        );

      case 1:
        return (
          <div>
            <Title level={4} style={{ marginBottom: 24 }}>
              {feedbackTypes.find(t => t.key === feedbackType)?.label}
            </Title>
            
            <Form form={form} layout="vertical">
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {categories[feedbackType].map(cat => (
                    <Option key={cat} value={cat}>{cat}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="title"
                label="标题"
                rules={[{ required: true, message: '请输入标题' }]}
              >
                <Input placeholder="简要描述您的反馈" />
              </Form.Item>

              <Form.Item
                name="description"
                label="详细描述"
                rules={[{ required: true, message: '请输入详细描述' }]}
              >
                <TextArea 
                  rows={4} 
                  placeholder="请详细描述您的反馈内容"
                />
              </Form.Item>

              {feedbackType === 'bug' && (
                <>
                  <Form.Item name="steps" label="重现步骤">
                    <TextArea 
                      rows={3} 
                      placeholder="请描述如何重现这个问题"
                    />
                  </Form.Item>
                  
                  <Form.Item name="expectedBehavior" label="期望行为">
                    <Input placeholder="您期望的正确行为是什么？" />
                  </Form.Item>
                  
                  <Form.Item name="actualBehavior" label="实际行为">
                    <Input placeholder="实际发生了什么？" />
                  </Form.Item>
                </>
              )}

              {feedbackType === 'usability' && (
                <Form.Item name="features" label="涉及功能">
                  <Checkbox.Group>
                    <Space direction="vertical">
                      <Checkbox value="login">登录注册</Checkbox>
                      <Checkbox value="subscription">订阅管理</Checkbox>
                      <Checkbox value="news">新闻浏览</Checkbox>
                      <Checkbox value="search">搜索功能</Checkbox>
                      <Checkbox value="profile">个人中心</Checkbox>
                      <Checkbox value="settings">系统设置</Checkbox>
                    </Space>
                  </Checkbox.Group>
                </Form.Item>
              )}
            </Form>
          </div>
        );

      case 2:
        return (
          <div>
            <Title level={4} style={{ marginBottom: 24 }}>
              评价使用体验
            </Title>
            
            <Form form={form} layout="vertical">
              <Form.Item name="rating" label="总体评分">
                <Rate 
                  character={({ index = 0 }) => {
                    if (index < 2) return <FrownOutlined />;
                    if (index < 4) return <MehOutlined />;
                    return <SmileOutlined />;
                  }}
                />
              </Form.Item>

              <Form.Item name="difficulty" label="使用难度">
                <div>
                  <Rate count={5} />
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">1星=非常简单，5星=非常困难</Text>
                  </div>
                </div>
              </Form.Item>

              <Form.Item name="satisfaction" label="满意度">
                <div>
                  <Rate count={5} />
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">1星=非常不满意，5星=非常满意</Text>
                  </div>
                </div>
              </Form.Item>

              <Form.Item name="recommendation" label="推荐度">
                <div>
                  <Rate count={10} />
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">您会向朋友推荐这个产品吗？</Text>
                  </div>
                </div>
              </Form.Item>
            </Form>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>用户反馈</span>
          <Button type="text" icon={<CloseOutlined />} onClick={onClose} />
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={isMobile ? '95%' : 600}
      footer={null}
      closable={false}
    >
      {/* 进度条 */}
      <div style={{ marginBottom: 24 }}>
        <Progress 
          percent={((currentStep + 1) / steps.length) * 100} 
          showInfo={false}
          strokeColor="#1890ff"
        />
        <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 8 }}>
          {steps.map((step, index) => (
            <div key={index} style={{ textAlign: 'center', flex: 1 }}>
              <Text 
                style={{ 
                  fontSize: 12,
                  color: index <= currentStep ? '#1890ff' : '#999'
                }}
              >
                {step.title}
              </Text>
            </div>
          ))}
        </div>
      </div>

      {/* 步骤内容 */}
      <div style={{ minHeight: 300, marginBottom: 24 }}>
        {renderStepContent()}
      </div>

      {/* 操作按钮 */}
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button onClick={onClose}>
          取消
        </Button>
        
        <Space>
          {currentStep > 0 && (
            <Button onClick={handlePrev}>
              上一步
            </Button>
          )}
          
          {currentStep < steps.length - 1 ? (
            <Button type="primary" onClick={handleNext}>
              下一步
            </Button>
          ) : (
            <Button 
              type="primary" 
              icon={<SendOutlined />}
              loading={isSubmitting}
              onClick={handleSubmit}
            >
              提交反馈
            </Button>
          )}
        </Space>
      </div>
    </Modal>
  );
};

export default FeedbackCollector;
