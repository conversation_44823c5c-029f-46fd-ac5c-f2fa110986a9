import React, { useState } from 'react';
import { Modal, Card, Button, Space, Typography, Row, Col, Tabs, Timeline, Tag } from 'antd';
import {
  RocketOutlined,
  BellOutlined,
  BarChartOutlined,
  SafetyCertificateOutlined,
  MobileOutlined,
  CloudOutlined,
  StarOutlined,
  GiftOutlined,
  PlayCircleOutlined,
  BookOutlined,
  SettingOutlined,
  HeartOutlined,
} from '@ant-design/icons';
import useResponsive from '../../hooks/useResponsive';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  benefits: string[];
  isNew?: boolean;
  isPremium?: boolean;
}

interface FeatureIntroductionProps {
  visible: boolean;
  onClose: () => void;
  onStartTour?: () => void;
  showNewFeatures?: boolean;
}

const FeatureIntroduction: React.FC<FeatureIntroductionProps> = ({
  visible,
  onClose,
  onStartTour,
  showNewFeatures = false,
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const { isMobile } = useResponsive();

  const coreFeatures: Feature[] = [
    {
      icon: <BellOutlined style={{ color: '#1890ff' }} />,
      title: '智能新闻推送',
      description: '基于AI算法的个性化财经新闻推荐',
      benefits: [
        '实时财经资讯推送',
        '个性化内容推荐',
        '多渠道推送支持',
        '智能去重过滤',
      ],
    },
    {
      icon: <BarChartOutlined style={{ color: '#52c41a' }} />,
      title: '数据分析洞察',
      description: '深度分析市场趋势和投资机会',
      benefits: [
        '市场趋势分析',
        '投资机会识别',
        '风险预警提醒',
        '数据可视化展示',
      ],
    },
    {
      icon: <SettingOutlined style={{ color: '#faad14' }} />,
      title: '订阅管理',
      description: '灵活配置您的新闻订阅偏好',
      benefits: [
        '多维度订阅配置',
        '关键词智能匹配',
        '推送时间自定义',
        '订阅效果统计',
      ],
    },
    {
      icon: <MobileOutlined style={{ color: '#722ed1' }} />,
      title: '多端同步',
      description: '支持Web、移动端、微信等多平台',
      benefits: [
        '跨平台数据同步',
        '响应式设计',
        '离线阅读支持',
        '云端备份恢复',
      ],
    },
  ];

  const newFeatures: Feature[] = [
    {
      icon: <RocketOutlined style={{ color: '#ff4d4f' }} />,
      title: 'AI智能摘要',
      description: '自动生成新闻摘要和关键信息提取',
      benefits: [
        '一键生成新闻摘要',
        '关键信息自动提取',
        '多语言支持',
        '情感分析',
      ],
      isNew: true,
    },
    {
      icon: <SafetyCertificateOutlined style={{ color: '#13c2c2' }} />,
      title: '信息可信度评估',
      description: '基于多源验证的新闻可信度评分',
      benefits: [
        '多源信息交叉验证',
        '可信度评分系统',
        '虚假信息识别',
        '来源追溯',
      ],
      isNew: true,
    },
    {
      icon: <CloudOutlined style={{ color: '#eb2f96' }} />,
      title: '智能投资建议',
      description: '基于新闻分析的投资建议和风险提示',
      benefits: [
        '投资机会识别',
        '风险评估分析',
        '投资组合建议',
        '市场情绪分析',
      ],
      isNew: true,
      isPremium: true,
    },
  ];

  const premiumFeatures: Feature[] = [
    {
      icon: <StarOutlined style={{ color: '#faad14' }} />,
      title: '高级分析报告',
      description: '专业级别的市场分析和投资报告',
      benefits: [
        '深度市场分析',
        '专业投资报告',
        '行业研究报告',
        '定制化分析',
      ],
      isPremium: true,
    },
    {
      icon: <GiftOutlined style={{ color: '#f759ab' }} />,
      title: '专属客服支持',
      description: '7x24小时专属客服和技术支持',
      benefits: [
        '专属客服通道',
        '优先技术支持',
        '定制化服务',
        '专业咨询建议',
      ],
      isPremium: true,
    },
  ];

  const renderFeatureCard = (feature: Feature) => (
    <Card
      key={feature.title}
      size="small"
      style={{ height: '100%' }}
      bodyStyle={{ padding: 16 }}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', marginBottom: 12 }}>
        <div style={{ fontSize: 24, marginRight: 12 }}>
          {feature.icon}
        </div>
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
            <Title level={5} style={{ margin: 0, marginRight: 8 }}>
              {feature.title}
            </Title>
            {feature.isNew && <Tag color="red">新功能</Tag>}
            {feature.isPremium && <Tag color="gold">高级版</Tag>}
          </div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {feature.description}
          </Text>
        </div>
      </div>
      
      <div>
        {feature.benefits.map((benefit, index) => (
          <div key={index} style={{ marginBottom: 4 }}>
            <Text style={{ fontSize: 12 }}>
              • {benefit}
            </Text>
          </div>
        ))}
      </div>
    </Card>
  );

  const renderUpdateTimeline = () => (
    <Timeline>
      <Timeline.Item color="blue">
        <div>
          <Text strong>v2.1.0</Text>
          <Text type="secondary" style={{ marginLeft: 8 }}>2024-01-15</Text>
        </div>
        <div style={{ marginTop: 4 }}>
          <Text>新增AI智能摘要功能</Text>
        </div>
      </Timeline.Item>
      <Timeline.Item color="green">
        <div>
          <Text strong>v2.0.5</Text>
          <Text type="secondary" style={{ marginLeft: 8 }}>2024-01-10</Text>
        </div>
        <div style={{ marginTop: 4 }}>
          <Text>优化移动端体验</Text>
        </div>
      </Timeline.Item>
      <Timeline.Item color="blue">
        <div>
          <Text strong>v2.0.0</Text>
          <Text type="secondary" style={{ marginLeft: 8 }}>2024-01-01</Text>
        </div>
        <div style={{ marginTop: 4 }}>
          <Text>全新界面设计，新增信息可信度评估</Text>
        </div>
      </Timeline.Item>
      <Timeline.Item>
        <div>
          <Text strong>v1.5.0</Text>
          <Text type="secondary" style={{ marginLeft: 8 }}>2023-12-15</Text>
        </div>
        <div style={{ marginTop: 4 }}>
          <Text>新增多端同步功能</Text>
        </div>
      </Timeline.Item>
    </Timeline>
  );

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <RocketOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          <span>功能介绍</span>
          {showNewFeatures && (
            <Tag color="red" style={{ marginLeft: 8 }}>
              有新功能
            </Tag>
          )}
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={isMobile ? '95%' : 800}
      footer={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button onClick={onClose}>
            稍后查看
          </Button>
          <Space>
            <Button icon={<BookOutlined />}>
              查看帮助文档
            </Button>
            {onStartTour && (
              <Button 
                type="primary" 
                icon={<PlayCircleOutlined />}
                onClick={() => {
                  onClose();
                  onStartTour();
                }}
              >
                开始引导
              </Button>
            )}
          </Space>
        </div>
      }
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="功能概览" key="overview">
          <div style={{ marginBottom: 16 }}>
            <Paragraph>
              财经新闻Bot为您提供智能化的财经资讯服务，
              通过AI技术为您筛选最有价值的新闻内容。
            </Paragraph>
          </div>
          
          <Row gutter={[16, 16]}>
            {coreFeatures.map((feature, index) => (
              <Col xs={24} sm={12} lg={12} key={index}>
                {renderFeatureCard(feature)}
              </Col>
            ))}
          </Row>
        </TabPane>

        {showNewFeatures && (
          <TabPane 
            tab={
              <span>
                新功能 
                <Tag color="red" style={{ marginLeft: 4 }}>
                  {newFeatures.length}
                </Tag>
              </span>
            } 
            key="new"
          >
            <div style={{ marginBottom: 16 }}>
              <Paragraph>
                我们持续为您带来更好的功能体验，以下是最新上线的功能：
              </Paragraph>
            </div>
            
            <Row gutter={[16, 16]}>
              {newFeatures.map((feature, index) => (
                <Col xs={24} sm={12} lg={8} key={index}>
                  {renderFeatureCard(feature)}
                </Col>
              ))}
            </Row>
          </TabPane>
        )}

        <TabPane tab="高级功能" key="premium">
          <div style={{ marginBottom: 16 }}>
            <div style={{ 
              padding: 16, 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: 8,
              color: 'white',
              marginBottom: 16
            }}>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                <StarOutlined style={{ fontSize: 20, marginRight: 8 }} />
                <Title level={4} style={{ margin: 0, color: 'white' }}>
                  升级到高级版
                </Title>
              </div>
              <Text style={{ color: 'rgba(255,255,255,0.9)' }}>
                解锁更多专业功能，获得更深度的财经分析和投资建议
              </Text>
            </div>
          </div>
          
          <Row gutter={[16, 16]}>
            {premiumFeatures.map((feature, index) => (
              <Col xs={24} sm={12} key={index}>
                {renderFeatureCard(feature)}
              </Col>
            ))}
          </Row>

          <div style={{ textAlign: 'center', marginTop: 24 }}>
            <Button type="primary" size="large" icon={<StarOutlined />}>
              立即升级高级版
            </Button>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                新用户享受30天免费试用
              </Text>
            </div>
          </div>
        </TabPane>

        <TabPane tab="更新日志" key="updates">
          <div style={{ marginBottom: 16 }}>
            <Paragraph>
              查看最新的功能更新和改进：
            </Paragraph>
          </div>
          
          {renderUpdateTimeline()}
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default FeatureIntroduction;
