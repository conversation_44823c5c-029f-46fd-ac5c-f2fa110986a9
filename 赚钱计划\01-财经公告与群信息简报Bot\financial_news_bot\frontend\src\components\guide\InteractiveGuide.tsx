import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>, Typography, Space, Tooltip, Badge } from 'antd';
import {
  QuestionCircleOutlined,
  CloseOutlined,
  ArrowRightOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import useResponsive from '../../hooks/useResponsive';

const { Text, Title } = Typography;

interface GuideStep {
  id: string;
  title: string;
  content: string;
  target: string; // CSS选择器
  position: 'top' | 'bottom' | 'left' | 'right' | 'center';
  action?: {
    text: string;
    onClick: () => void;
  };
  highlight?: boolean;
  delay?: number; // 延迟显示时间（毫秒）
}

interface InteractiveGuideProps {
  steps: GuideStep[];
  visible: boolean;
  onComplete: () => void;
  onSkip: () => void;
  autoStart?: boolean;
  showProgress?: boolean;
}

const InteractiveGuide: React.FC<InteractiveGuideProps> = ({
  steps,
  visible,
  onComplete,
  onSkip,
  autoStart = true,
  showProgress = true,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [overlayPosition, setOverlayPosition] = useState({ top: 0, left: 0 });
  const overlayRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useResponsive();

  const currentGuideStep = steps[currentStep];

  // 查找目标元素
  useEffect(() => {
    if (!visible || !currentGuideStep) return;

    const findTarget = () => {
      const element = document.querySelector(currentGuideStep.target) as HTMLElement;
      if (element) {
        setTargetElement(element);
        calculatePosition(element);
      } else {
        // 如果找不到目标元素，延迟重试
        setTimeout(findTarget, 100);
      }
    };

    const timer = setTimeout(findTarget, currentGuideStep.delay || 0);
    return () => clearTimeout(timer);
  }, [visible, currentStep, currentGuideStep]);

  // 计算引导框位置
  const calculatePosition = (target: HTMLElement) => {
    if (!target) return;

    const rect = target.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    let top = 0;
    let left = 0;

    switch (currentGuideStep.position) {
      case 'top':
        top = rect.top + scrollTop - 10;
        left = rect.left + scrollLeft + rect.width / 2;
        break;
      case 'bottom':
        top = rect.bottom + scrollTop + 10;
        left = rect.left + scrollLeft + rect.width / 2;
        break;
      case 'left':
        top = rect.top + scrollTop + rect.height / 2;
        left = rect.left + scrollLeft - 10;
        break;
      case 'right':
        top = rect.top + scrollTop + rect.height / 2;
        left = rect.right + scrollLeft + 10;
        break;
      case 'center':
        top = rect.top + scrollTop + rect.height / 2;
        left = rect.left + scrollLeft + rect.width / 2;
        break;
    }

    setOverlayPosition({ top, left });
  };

  // 高亮目标元素
  useEffect(() => {
    if (!targetElement || !currentGuideStep?.highlight) return;

    const originalStyle = {
      position: targetElement.style.position,
      zIndex: targetElement.style.zIndex,
      boxShadow: targetElement.style.boxShadow,
      border: targetElement.style.border,
    };

    // 添加高亮样式
    targetElement.style.position = 'relative';
    targetElement.style.zIndex = '1001';
    targetElement.style.boxShadow = '0 0 0 4px rgba(24, 144, 255, 0.3), 0 0 0 8px rgba(24, 144, 255, 0.1)';
    targetElement.style.border = '2px solid #1890ff';
    targetElement.style.borderRadius = '4px';

    // 滚动到目标元素
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center',
    });

    return () => {
      // 恢复原始样式
      Object.assign(targetElement.style, originalStyle);
    };
  }, [targetElement, currentGuideStep]);

  // 启动引导
  useEffect(() => {
    if (visible && autoStart) {
      setIsActive(true);
    }
  }, [visible, autoStart]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleComplete = () => {
    setIsActive(false);
    onComplete();
  };

  const handleSkipGuide = () => {
    setIsActive(false);
    onSkip();
  };

  // 执行步骤动作
  const handleStepAction = () => {
    if (currentGuideStep.action) {
      currentGuideStep.action.onClick();
    }
    handleNext();
  };

  if (!visible || !isActive || !currentGuideStep) {
    return null;
  }

  const guideCardStyle: React.CSSProperties = {
    position: 'absolute',
    top: overlayPosition.top,
    left: overlayPosition.left,
    zIndex: 1002,
    maxWidth: isMobile ? 280 : 320,
    transform: isMobile ? 'translate(-50%, -100%)' : getTransform(),
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
    border: '1px solid #1890ff',
  };

  function getTransform() {
    switch (currentGuideStep.position) {
      case 'top':
        return 'translate(-50%, -100%)';
      case 'bottom':
        return 'translate(-50%, 0)';
      case 'left':
        return 'translate(-100%, -50%)';
      case 'right':
        return 'translate(0, -50%)';
      case 'center':
        return 'translate(-50%, -50%)';
      default:
        return 'translate(-50%, -50%)';
    }
  }

  return (
    <>
      {/* 遮罩层 */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          zIndex: 1000,
          pointerEvents: 'none',
        }}
      />

      {/* 引导卡片 */}
      <Card
        ref={overlayRef}
        style={guideCardStyle}
        bodyStyle={{ padding: 16 }}
        size="small"
      >
        {/* 头部 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
          <Space>
            <Badge count={currentStep + 1} size="small" color="#1890ff" />
            <Title level={5} style={{ margin: 0 }}>
              {currentGuideStep.title}
            </Title>
          </Space>
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined />}
            onClick={handleSkipGuide}
          />
        </div>

        {/* 内容 */}
        <div style={{ marginBottom: 16 }}>
          <Text>{currentGuideStep.content}</Text>
        </div>

        {/* 进度条 */}
        {showProgress && (
          <div style={{ marginBottom: 16 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                步骤 {currentStep + 1} / {steps.length}
              </Text>
              <Text type="secondary" style={{ fontSize: 12 }}>
                {Math.round(((currentStep + 1) / steps.length) * 100)}%
              </Text>
            </div>
            <div style={{ 
              height: 4, 
              backgroundColor: '#f0f0f0', 
              borderRadius: 2,
              overflow: 'hidden'
            }}>
              <div style={{
                height: '100%',
                width: `${((currentStep + 1) / steps.length) * 100}%`,
                backgroundColor: '#1890ff',
                transition: 'width 0.3s ease',
              }} />
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button size="small" onClick={handleSkipGuide} type="text">
            跳过
          </Button>
          
          <Space size="small">
            {currentStep > 0 && (
              <Button size="small" onClick={handlePrev}>
                上一步
              </Button>
            )}
            
            {currentGuideStep.action ? (
              <Button 
                type="primary" 
                size="small" 
                onClick={handleStepAction}
                icon={<ArrowRightOutlined />}
              >
                {currentGuideStep.action.text}
              </Button>
            ) : (
              <Button 
                type="primary" 
                size="small" 
                onClick={handleNext}
                icon={currentStep === steps.length - 1 ? <CheckCircleOutlined /> : <ArrowRightOutlined />}
              >
                {currentStep === steps.length - 1 ? '完成' : '下一步'}
              </Button>
            )}
          </Space>
        </div>

        {/* 箭头指示器 */}
        {!isMobile && (
          <div
            style={{
              position: 'absolute',
              width: 0,
              height: 0,
              ...getArrowStyle(),
            }}
          />
        )}
      </Card>
    </>
  );

  function getArrowStyle() {
    const arrowSize = 8;
    const borderColor = '#1890ff';
    
    switch (currentGuideStep.position) {
      case 'top':
        return {
          bottom: -arrowSize,
          left: '50%',
          marginLeft: -arrowSize,
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderTop: `${arrowSize}px solid ${borderColor}`,
        };
      case 'bottom':
        return {
          top: -arrowSize,
          left: '50%',
          marginLeft: -arrowSize,
          borderLeft: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid ${borderColor}`,
        };
      case 'left':
        return {
          right: -arrowSize,
          top: '50%',
          marginTop: -arrowSize,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderLeft: `${arrowSize}px solid ${borderColor}`,
        };
      case 'right':
        return {
          left: -arrowSize,
          top: '50%',
          marginTop: -arrowSize,
          borderTop: `${arrowSize}px solid transparent`,
          borderBottom: `${arrowSize}px solid transparent`,
          borderRight: `${arrowSize}px solid ${borderColor}`,
        };
      default:
        return {};
    }
  }
};

export default InteractiveGuide;
