import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import {
  Layout,
  Menu,
  Avatar,
  Dropdown,
  Badge,
  Button,
  Drawer,
  Space,
  Typography,
  Divider,
  FloatButton,
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  FileTextOutlined,
  BellOutlined,
  HeartOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BookOutlined,
  PlusOutlined,
  SearchOutlined,
  UpOutlined,
} from '@ant-design/icons';

import { useAppDispatch, useAppSelector } from '../../store';
import { logout } from '../../store/slices/authSlice';
import useKeyboardShortcuts, { commonShortcuts } from '../../hooks/useKeyboardShortcuts';
import useResponsive, { useResponsiveValue } from '../../hooks/useResponsive';
import GlobalSearch from '../search/GlobalSearch';
import UserGuide from '../guide/UserGuide';
import { toggleSidebar, setDrawerVisible, setIsMobile } from '../../store/slices/uiSlice';
import { MenuItem } from '../../types';
import type { MenuProps } from 'antd';

import Breadcrumb from '../common/Breadcrumb';
import NotificationPanel from '../common/NotificationPanel';
import MobileBottomNav from './MobileBottomNav';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const { user } = useAppSelector(state => state.auth);
  const { sidebarCollapsed, notifications } = useAppSelector(state => state.ui);

  // 使用响应式Hook
  const responsive = useResponsive();
  const { isMobile, isTablet, isDesktop, isTouchDevice, currentBreakpoint } = responsive;

  const [notificationVisible, setNotificationVisible] = useState(false);
  const [searchVisible, setSearchVisible] = useState(false);
  const [guideVisible, setGuideVisible] = useState(false);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // 响应式值配置
  const siderWidth = useResponsiveValue({
    xs: 200,
    sm: 220,
    md: 240,
    lg: 260,
    default: 240,
  });

  const headerPadding = useResponsiveValue({
    xs: '0 12px',
    sm: '0 16px',
    md: '0 20px',
    lg: '0 24px',
    default: '0 24px',
  });

  const contentMargin = useResponsiveValue({
    xs: 8,
    sm: 12,
    md: 16,
    lg: 20,
    xl: 24,
    default: 24,
  });

  // 同步移动端状态到Redux
  useEffect(() => {
    dispatch(setIsMobile(isMobile));
  }, [isMobile, dispatch]);

  // 监听滚动，显示/隐藏回到顶部按钮
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.pageYOffset > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 转换菜单项为Antd格式
  const convertToAntdMenuItems = (items: MenuItem[]): MenuProps['items'] => {
    return items.map(item => ({
      key: item.key,
      label: item.label,
      icon: item.icon,
      children: item.children ? convertToAntdMenuItems(item.children) : undefined,
    }));
  };

  // 菜单配置
  const menuItems: MenuItem[] = [
    {
      key: '/dashboard',
      label: '仪表板',
      icon: <DashboardOutlined />,
      path: '/dashboard',
    },
    {
      key: '/news',
      label: '新闻中心',
      icon: <FileTextOutlined />,
      path: '/news',
    },
    {
      key: '/bookmarks',
      label: '我的收藏',
      icon: <HeartOutlined />,
      path: '/bookmarks',
    },
    {
      key: '/subscriptions',
      label: '订阅管理',
      icon: <BookOutlined />,
      path: '/subscriptions',
      children: [
        {
          key: '/subscriptions',
          label: '我的订阅',
          path: '/subscriptions',
        },
        {
          key: '/subscriptions/create',
          label: '创建订阅',
          icon: <PlusOutlined />,
          path: '/subscriptions/create',
        },
      ],
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人资料',
      icon: <UserOutlined />,
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      label: '系统设置',
      icon: <SettingOutlined />,
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  // 快捷键配置
  const shortcuts = [
    commonShortcuts.goToHome(() => navigate('/dashboard')),
    commonShortcuts.goToNews(() => navigate('/news')),
    commonShortcuts.goToSubscriptions(() => navigate('/subscriptions')),
    commonShortcuts.goToProfile(() => navigate('/profile')),
    commonShortcuts.newSubscription(() => navigate('/subscriptions/create')),
    commonShortcuts.search(() => setSearchVisible(true)),
    commonShortcuts.refresh(() => window.location.reload()),
    commonShortcuts.toggleTheme(() => {
      // 切换主题逻辑
      console.log('切换主题');
    }),
    {
      key: 'g',
      ctrl: true,
      shift: true,
      description: '显示新手引导',
      action: () => setGuideVisible(true),
    },
  ];

  // 注册快捷键
  useKeyboardShortcuts(shortcuts);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    if (isMobile) {
      dispatch(setDrawerVisible(false));
    }
  };

  // 处理登出
  function handleLogout() {
    dispatch(logout());
    navigate('/auth/login');
  }

  // 切换侧边栏
  const handleToggleSidebar = () => {
    if (isMobile) {
      dispatch(setDrawerVisible(true));
    } else {
      dispatch(toggleSidebar());
    }
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    // 精确匹配或父级匹配
    if (path.startsWith('/subscriptions')) {
      return ['/subscriptions'];
    }
    return [path];
  };

  // 获取展开的菜单项
  const getOpenKeys = () => {
    const path = location.pathname;
    if (path.startsWith('/subscriptions')) {
      return ['/subscriptions'];
    }
    return [];
  };

  // 渲染菜单
  const renderMenu = () => (
    <Menu
      theme="dark"
      mode="inline"
      selectedKeys={getSelectedKeys()}
      defaultOpenKeys={getOpenKeys()}
      onClick={handleMenuClick}
      items={convertToAntdMenuItems(menuItems)}
    />
  );

  // 未读通知数量
  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <Layout className="full-height">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={sidebarCollapsed}
          width={siderWidth}
          collapsedWidth={isTablet ? 60 : 80}
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            zIndex: 100,
          }}
          data-guide="sidebar"
        >
          <div
            style={{
              height: 64,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: sidebarCollapsed ? 14 : 18,
              fontWeight: 'bold',
              padding: '0 8px',
            }}
          >
            {sidebarCollapsed ? '财经Bot' : '财经新闻Bot'}
          </div>
          {renderMenu()}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title="财经新闻Bot"
          placement="left"
          onClose={() => dispatch(setDrawerVisible(false))}
          open={useAppSelector(state => state.ui.drawerVisible)}
          bodyStyle={{ padding: 0 }}
          width={siderWidth}
          styles={{
            body: { padding: 0 },
            header: {
              padding: '16px 24px',
              borderBottom: '1px solid #f0f0f0',
            },
          }}
        >
          {renderMenu()}
        </Drawer>
      )}

      <Layout
        style={{
          marginLeft: !isMobile ? (sidebarCollapsed ? (isTablet ? 60 : 80) : siderWidth) : 0,
          transition: 'margin-left 0.2s ease',
          minHeight: '100vh',
        }}
      >
        {/* 头部 */}
        <Header
          className={isMobile ? 'mobile-sticky-header' : ''}
          style={{
            padding: headerPadding,
            background: '#fff',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 4px rgba(0,21,41,.08)',
            height: isMobile ? 56 : 64,
            lineHeight: isMobile ? '56px' : '64px',
            position: isMobile ? 'sticky' : 'static',
            top: 0,
            zIndex: 99,
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
            <Button
              type="text"
              icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={handleToggleSidebar}
              style={{
                fontSize: 16,
                width: isTouchDevice ? 44 : 40,
                height: isTouchDevice ? 44 : 40,
                minWidth: isTouchDevice ? 44 : 40,
              }}
              className={isTouchDevice ? 'touch-target' : ''}
            />
            {!isMobile && <Breadcrumb />}
          </div>

          <Space size={isMobile ? 'small' : 'middle'}>
            {/* 搜索 */}
            <Button
              type="text"
              icon={<SearchOutlined />}
              onClick={() => setSearchVisible(true)}
              style={{
                fontSize: 16,
                width: isTouchDevice ? 44 : 40,
                height: isTouchDevice ? 44 : 40,
                minWidth: isTouchDevice ? 44 : 40,
              }}
              title="全局搜索 (Ctrl+K)"
              className={isTouchDevice ? 'touch-target' : ''}
              data-guide="search"
              data-tip="search"
            />

            {/* 通知 */}
            <Badge count={unreadCount} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                onClick={() => setNotificationVisible(true)}
                style={{
                  fontSize: 16,
                  width: isTouchDevice ? 44 : 40,
                  height: isTouchDevice ? 44 : 40,
                  minWidth: isTouchDevice ? 44 : 40,
                }}
                className={isTouchDevice ? 'touch-target' : ''}
                data-guide="notifications"
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space
                style={{ cursor: 'pointer' }}
                className={isTouchDevice ? 'touch-target' : ''}
                data-guide="profile"
              >
                <Avatar
                  size={isMobile ? 'small' : 'default'}
                  src={user?.avatar}
                  icon={<UserOutlined />}
                />
                {!isMobile && <Text strong>{user?.full_name || user?.username}</Text>}
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content
          style={{
            margin: contentMargin,
            padding: isMobile ? 12 : contentMargin,
            background: '#fff',
            borderRadius: isMobile ? 4 : 8,
            minHeight: `calc(100vh - ${isMobile ? 88 : 112}px)`,
            overflow: 'auto',
          }}
          className={isMobile ? 'mobile-safe-area' : ''}
        >
          {/* 移动端面包屑 */}
          {isMobile && (
            <div style={{ marginBottom: 16, paddingBottom: 8, borderBottom: '1px solid #f0f0f0' }}>
              <Breadcrumb />
            </div>
          )}
          <Outlet />
        </Content>
      </Layout>

      {/* 回到顶部按钮 */}
      {showBackToTop && (
        <FloatButton
          icon={<UpOutlined />}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          style={{
            right: isMobile ? 16 : 24,
            bottom: isMobile ? 80 : 24,
          }}
        />
      )}

      {/* 通知面板 */}
      <NotificationPanel
        visible={notificationVisible}
        onClose={() => setNotificationVisible(false)}
      />

      {/* 全局搜索 */}
      <GlobalSearch
        visible={searchVisible}
        onClose={() => setSearchVisible(false)}
      />

      {/* 新手引导 */}
      <UserGuide
        visible={guideVisible}
        onClose={() => setGuideVisible(false)}
        onComplete={() => {
          setGuideVisible(false);
          // 标记用户已完成引导
          localStorage.setItem('user_guide_completed', 'true');
        }}
        onSkip={() => {
          setGuideVisible(false);
          // 标记用户跳过引导
          localStorage.setItem('user_guide_skipped', 'true');
        }}
      />

      {/* 移动端底部导航 */}
      <MobileBottomNav />
    </Layout>
  );
};

export default MainLayout;
