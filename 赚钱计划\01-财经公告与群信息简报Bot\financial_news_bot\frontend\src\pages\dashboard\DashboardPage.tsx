import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Button } from 'antd';
import {
  FileTextOutlined,
  BookOutlined,
  BellOutlined,
  TrophyOutlined,
  PlusOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import { setPageTitle } from '../../store/slices/uiSlice';

const { Title, Text } = Typography;

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);

  useEffect(() => {
    dispatch(setPageTitle('仪表板'));
  }, [dispatch]);

  // 真实统计数据状态
  const [stats, setStats] = React.useState({
    totalNews: 0,
    todayNews: 0,
    subscriptions: 0,
    pushCount: 0,
    loading: true,
  });

  // 加载真实统计数据
  const loadStats = async () => {
    try {
      setStats(prev => ({ ...prev, loading: true }));

      // 获取新闻统计
      const newsResponse = await fetch('/api/v1/news/statistics/overview');
      const newsData = newsResponse.ok ? await newsResponse.json() : { total_count: 0, today_count: 0 };

      // 获取订阅统计
      const subscriptionResponse = await fetch('/api/v1/subscriptions/');
      const subscriptionData = subscriptionResponse.ok ? await subscriptionResponse.json() : [];

      // 获取推送统计
      const pushResponse = await fetch('/api/v1/push/statistics');
      const pushData = pushResponse.ok ? await pushResponse.json() : { total_pushes: 0 };

      setStats({
        totalNews: newsData.total_count || 0,
        todayNews: newsData.today_count || 0,
        subscriptions: Array.isArray(subscriptionData) ? subscriptionData.length : 0,
        pushCount: pushData.total_pushes || 0,
        loading: false,
      });
    } catch (error) {
      console.error('加载统计数据失败:', error);
      setStats(prev => ({ ...prev, loading: false }));
    }
  };

  // 页面加载时获取统计数据
  useEffect(() => {
    loadStats();
  }, []);

  return (
    <div>
      {/* 欢迎区域 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ marginBottom: '8px' }}>
          欢迎回来，{user?.full_name || user?.username}！
        </Title>
        <Text type="secondary">
          今天是 {new Date().toLocaleDateString('zh-CN', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            weekday: 'long'
          })}
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={stats.loading}>
            <Statistic
              title="总新闻数"
              value={stats.totalNews}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={stats.loading}>
            <Statistic
              title="今日新增"
              value={stats.todayNews}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={stats.loading}>
            <Statistic
              title="我的订阅"
              value={stats.subscriptions}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="推送次数"
              value={stats.pushCount}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={12}>
          <Card title="快速操作" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/subscriptions/create')}
                block
              >
                创建新订阅
              </Button>
              <Button
                icon={<EyeOutlined />}
                onClick={() => navigate('/news')}
                block
              >
                浏览最新新闻
              </Button>
              <Button
                icon={<BookOutlined />}
                onClick={() => navigate('/subscriptions')}
                block
              >
                管理我的订阅
              </Button>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="最近活动" size="small">
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <Text type="secondary">暂无最近活动</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最新新闻预览 */}
      <Card title="最新财经新闻" size="small">
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">正在加载最新新闻...</Text>
        </div>
      </Card>
    </div>
  );
};

export default DashboardPage;
