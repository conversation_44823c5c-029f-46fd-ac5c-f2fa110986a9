import React, { useEffect, useState } from 'react';
import {
  Card,
  Typography,
  Input,
  Select,
  Button,
  List,
  Tag,
  Space,
  Avatar,
  Pagination,
  Row,
  Col,
  DatePicker,
  Drawer,
  Form,
  Switch,
  Divider,
  Badge,
  Tooltip,
  message,
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  EyeOutlined,
  ShareAltOutlined,
  HeartOutlined,
  HeartFilled,
  ClockCircleOutlined,
  FireOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

import { useAppDispatch, useAppSelector } from '../../store';
import { setPageTitle } from '../../store/slices/uiSlice';
import TouchOptimized from '../../components/common/TouchOptimized';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface NewsItem {
  id: number;
  title: string;
  summary: string;
  content: string;
  source: string;
  author: string;
  published_at: string;
  importance: 'high' | 'medium' | 'low';
  category: string;
  tags: string[];
  views: number;
  likes: number;
  is_liked: boolean;
  is_bookmarked: boolean;
  image_url?: string;
}

const NewsPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [newsList, setNewsList] = useState<NewsItem[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filterVisible, setFilterVisible] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'card'>('list');

  const [filters, setFilters] = useState({
    category: '',
    source: '',
    importance: '',
    dateRange: null as any,
    tags: [] as string[],
  });

  useEffect(() => {
    dispatch(setPageTitle('新闻中心'));
    loadNews();
  }, [dispatch, currentPage, pageSize, searchKeyword, filters]);

  // 真实新闻数据状态（移除所有模拟数据）

  const loadNews = async () => {
    setLoading(true);
    try {
      // 真实API调用
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        ...(searchKeyword && { search: searchKeyword }),
        ...(filters.category && { category: filters.category }),
        ...(filters.source && { source: filters.source }),
        ...(filters.importance && { importance: filters.importance }),
      });

      const response = await fetch(`/api/v1/news/?${params}`);

      if (response.ok) {
        const data = await response.json();
        setNewsList(data.items || []);
        setTotal(data.total || 0);
      } else {
        message.error('加载新闻失败，请检查网络连接');
        setNewsList([]);
        setTotal(0);
      }
    } catch (error) {
      console.error('加载新闻失败:', error);
      message.error('加载新闻失败，请稍后重试');
      setNewsList([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1);
  };

  const handleFilter = (values: any) => {
    setFilters(values);
    setCurrentPage(1);
    setFilterVisible(false);
    message.success('筛选条件已应用');
  };

  const handleLike = (newsId: number) => {
    setNewsList(prev => prev.map(news =>
      news.id === newsId
        ? {
            ...news,
            is_liked: !news.is_liked,
            likes: news.is_liked ? news.likes - 1 : news.likes + 1
          }
        : news
    ));
  };

  const handleBookmark = (newsId: number) => {
    setNewsList(prev => prev.map(news =>
      news.id === newsId
        ? { ...news, is_bookmarked: !news.is_bookmarked }
        : news
    ));
    message.success('收藏状态已更新');
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };

  const getImportanceText = (importance: string) => {
    switch (importance) {
      case 'high': return '重要';
      case 'medium': return '一般';
      case 'low': return '普通';
      default: return '';
    }
  };

  const renderNewsItem = (item: NewsItem) => (
    <List.Item
      key={item.id}
      actions={[
        <Tooltip title="查看详情">
          <Button type="text" icon={<EyeOutlined />} onClick={() => window.open(`/news/${item.id}`)}>
            {item.views}
          </Button>
        </Tooltip>,
        <Tooltip title={item.is_liked ? '取消点赞' : '点赞'}>
          <Button
            type="text"
            icon={item.is_liked ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
            onClick={() => handleLike(item.id)}
          >
            {item.likes}
          </Button>
        </Tooltip>,
        <Tooltip title={item.is_bookmarked ? '取消收藏' : '收藏'}>
          <Button
            type="text"
            icon={<HeartOutlined />}
            style={{ color: item.is_bookmarked ? '#1890ff' : undefined }}
            onClick={() => handleBookmark(item.id)}
          />
        </Tooltip>,
        <Tooltip title="分享">
          <Button type="text" icon={<ShareAltOutlined />} />
        </Tooltip>,
      ]}
      extra={
        item.image_url && (
          <img
            width={200}
            height={120}
            alt={item.title}
            src={item.image_url}
            style={{ borderRadius: '6px', objectFit: 'cover' }}
          />
        )
      }
    >
      <List.Item.Meta
        avatar={
          <Badge dot={item.importance === 'high'}>
            <Avatar icon={<GlobalOutlined />} />
          </Badge>
        }
        title={
          <div>
            <Space>
              <Text strong style={{ fontSize: '16px' }}>{item.title}</Text>
              <Tag color={getImportanceColor(item.importance)}>
                {getImportanceText(item.importance)}
              </Tag>
              {item.importance === 'high' && <FireOutlined style={{ color: '#ff4d4f' }} />}
            </Space>
          </div>
        }
        description={
          <div>
            <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: '8px' }}>
              {item.summary}
            </Paragraph>
            <Space size="middle">
              <Text type="secondary">
                <ClockCircleOutlined /> {dayjs(item.published_at).format('MM-DD HH:mm')}
              </Text>
              <Text type="secondary">来源：{item.source}</Text>
              <Text type="secondary">作者：{item.author}</Text>
            </Space>
            <div style={{ marginTop: '8px' }}>
              <Space wrap>
                {item.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>
            </div>
          </div>
        }
      />
    </List.Item>
  );

  return (
    <div>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ marginBottom: '16px' }}>
          新闻中心
        </Title>

        {/* 搜索和筛选 */}
        <Row gutter={16} style={{ marginBottom: '16px' }}>
          <Col flex="auto">
            <Search
              placeholder="搜索新闻标题、内容或标签..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={handleSearch}
            />
          </Col>
          <Col>
            <Button
              icon={<FilterOutlined />}
              size="large"
              onClick={() => setFilterVisible(true)}
            >
              筛选
            </Button>
          </Col>
        </Row>

        {/* 快速筛选 */}
        <Row gutter={16} align="middle">
          <Col>
            <Text>分类：</Text>
            <Select
              placeholder="全部分类"
              style={{ width: 120, marginLeft: 8 }}
              allowClear
              onChange={(value) => setFilters(prev => ({ ...prev, category: value || '' }))}
            >
              <Option value="财经">财经</Option>
              <Option value="科技">科技</Option>
              <Option value="汽车">汽车</Option>
              <Option value="政策">政策</Option>
            </Select>
          </Col>
          <Col>
            <Text>重要程度：</Text>
            <Select
              placeholder="全部"
              style={{ width: 100, marginLeft: 8 }}
              allowClear
              onChange={(value) => setFilters(prev => ({ ...prev, importance: value || '' }))}
            >
              <Option value="high">重要</Option>
              <Option value="medium">一般</Option>
              <Option value="low">普通</Option>
            </Select>
          </Col>
          <Col>
            <Text>视图：</Text>
            <Select
              value={viewMode}
              style={{ width: 100, marginLeft: 8 }}
              onChange={setViewMode}
            >
              <Option value="list">列表</Option>
              <Option value="card">卡片</Option>
            </Select>
          </Col>
        </Row>
      </div>

      {/* 新闻列表 */}
      <Card>
        <TouchOptimized
          enablePullRefresh={true}
          enableLoadMore={true}
          onPullRefresh={async () => {
            await loadNews();
          }}
          onLoadMore={async () => {
            // 真实加载更多数据
            if (newsList.length < total) {
              setCurrentPage(prev => prev + 1);
            }
          }}
        >
          <List
            loading={loading}
            itemLayout="vertical"
            size="large"
            dataSource={newsList}
            renderItem={renderNewsItem}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, size) => {
                setCurrentPage(page);
                setPageSize(size || 10);
              },
            }}
          />
        </TouchOptimized>
      </Card>

      {/* 高级筛选抽屉 */}
      <Drawer
        title="高级筛选"
        placement="right"
        onClose={() => setFilterVisible(false)}
        open={filterVisible}
        width={400}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFilter}
          initialValues={filters}
        >
          <Form.Item name="category" label="新闻分类">
            <Select placeholder="选择分类" allowClear>
              <Option value="财经">财经</Option>
              <Option value="科技">科技</Option>
              <Option value="汽车">汽车</Option>
              <Option value="政策">政策</Option>
            </Select>
          </Form.Item>

          <Form.Item name="source" label="新闻来源">
            <Select placeholder="选择来源" allowClear>
              <Option value="新华网">新华网</Option>
              <Option value="36氪">36氪</Option>
              <Option value="财新网">财新网</Option>
              <Option value="人民网">人民网</Option>
            </Select>
          </Form.Item>

          <Form.Item name="importance" label="重要程度">
            <Select placeholder="选择重要程度" allowClear>
              <Option value="high">重要</Option>
              <Option value="medium">一般</Option>
              <Option value="low">普通</Option>
            </Select>
          </Form.Item>

          <Form.Item name="dateRange" label="发布时间">
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="tags" label="标签">
            <Select mode="multiple" placeholder="选择标签" allowClear>
              <Option value="货币政策">货币政策</Option>
              <Option value="人工智能">人工智能</Option>
              <Option value="新能源">新能源</Option>
              <Option value="科技">科技</Option>
            </Select>
          </Form.Item>

          <Divider />

          <Space>
            <Button type="primary" htmlType="submit">
              应用筛选
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form>
      </Drawer>
    </div>
  );
};

export default NewsPage;
