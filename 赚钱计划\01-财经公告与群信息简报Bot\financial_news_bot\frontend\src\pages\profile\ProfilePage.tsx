import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  Avatar,
  Row,
  Col,
  Typography,
  Divider,
  message,
  Progress,
  Tag,
  Space,
  Modal,
  Tabs,
  List,
  Statistic,
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
  EditOutlined,
  CameraOutlined,
  SafetyOutlined,
  HistoryOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import type { UploadProps } from 'antd';

import { useAppDispatch, useAppSelector } from '../../store';
import { setPageTitle } from '../../store/slices/uiSlice';
import { updateProfile } from '../../store/slices/authSlice';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface UserProfile {
  id: number;
  username: string;
  email: string;
  full_name: string;
  phone?: string;
  company?: string;
  position?: string;
  bio?: string;
  avatar?: string;
  location?: string;
  website?: string;
  created_at: string;
  last_login: string;
  profile_completeness: number;
  subscription_count: number;
  news_read_count: number;
  total_likes: number;
}

interface LoginHistory {
  id: number;
  ip_address: string;
  location: string;
  device: string;
  login_time: string;
  success: boolean;
}

const ProfilePage: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();

  const [loading, setLoading] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);

  // 加载用户资料
  const loadUserProfile = async () => {
    try {
      setProfileLoading(true);
      const response = await fetch('/api/v1/users/me', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserProfile(data);
        form.setFieldsValue(data);
      } else {
        message.error('获取用户资料失败');
      }
    } catch (error) {
      console.error('加载用户资料失败:', error);
      message.error('加载用户资料失败，请稍后重试');
    } finally {
      setProfileLoading(false);
    }
  };

  const [loginHistory] = useState<LoginHistory[]>([
    {
      id: 1,
      ip_address: '*************',
      location: '北京市',
      device: 'Chrome 120 / Windows 10',
      login_time: '2024-01-15T09:30:00Z',
      success: true,
    },
    {
      id: 2,
      ip_address: '*************',
      location: '北京市',
      device: 'Safari 17 / iPhone',
      login_time: '2024-01-14T18:45:00Z',
      success: true,
    },
  ]);

  useEffect(() => {
    dispatch(setPageTitle('个人资料'));
    loadUserProfile();
  }, [dispatch]);

  useEffect(() => {
    if (userProfile) {
      form.setFieldsValue(userProfile);
    }
  }, [form, userProfile]);

  const handleProfileUpdate = async (values: any) => {
    setLoading(true);
    try {
      // 真实API调用
      const response = await fetch('/api/v1/users/me', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify(values),
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setUserProfile(updatedProfile);
        message.success('个人资料更新成功');

        // 更新Redux中的用户信息
        dispatch(updateProfile(updatedProfile));
      } else {
        const error = await response.text();
        message.error(`更新失败: ${error}`);
      }
    } catch (error) {
      console.error('更新个人资料失败:', error);
      message.error('更新失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (values: any) => {
    try {
      // 真实API调用
      const response = await fetch('/api/v1/users/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: JSON.stringify({
          current_password: values.currentPassword,
          new_password: values.newPassword,
        }),
      });

      if (response.ok) {
        message.success('密码修改成功');
        setPasswordModalVisible(false);
        passwordForm.resetFields();
      } else {
        const error = await response.text();
        message.error(`密码修改失败: ${error}`);
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('密码修改失败，请检查网络连接');
    }
  };

  const handleAvatarUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;
    setAvatarLoading(true);

    try {
      // 真实头像上传
      const formData = new FormData();
      formData.append('avatar', file as File);

      const response = await fetch('/api/v1/users/avatar', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        setUserProfile(prev => prev ? { ...prev, avatar: data.avatar_url } : null);
        message.success('头像上传成功');
        onSuccess?.(data);
      } else {
        const error = await response.text();
        message.error(`头像上传失败: ${error}`);
        onError?.(new Error(error));
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      message.error('头像上传失败，请检查网络连接');
      onError?.(error as Error);
    } finally {
      setAvatarLoading(false);
    }
  };

  const getProfileCompletenessItems = () => {
    if (!userProfile) {
      return { items: [], completeness: 0 };
    }

    const items = [
      { key: 'avatar', label: '头像', completed: !!userProfile.avatar },
      { key: 'full_name', label: '姓名', completed: !!userProfile.full_name },
      { key: 'phone', label: '手机号', completed: !!userProfile.phone },
      { key: 'company', label: '公司', completed: !!userProfile.company },
      { key: 'position', label: '职位', completed: !!userProfile.position },
      { key: 'bio', label: '个人简介', completed: !!userProfile.bio },
    ];

    const completedCount = items.filter(item => item.completed).length;
    const completeness = Math.round((completedCount / items.length) * 100);

    return { items, completeness };
  };

  const { items: completenessItems, completeness } = getProfileCompletenessItems();

  return (
    <div>
      <Title level={2} style={{ marginBottom: '24px' }}>
        个人资料
      </Title>

      <Row gutter={24}>
        {/* 左侧主要内容 */}
        <Col xs={24} lg={16}>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="基本信息" key="profile">
              <Card>
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleProfileUpdate}
                  initialValues={userProfile || {}}
                >
                  {/* 头像上传 */}
                  <Form.Item label="头像">
                    <div style={{ textAlign: 'center', marginBottom: '16px' }}>
                      <Upload
                        name="avatar"
                        listType="picture-card"
                        className="avatar-uploader"
                        showUploadList={false}
                        customRequest={handleAvatarUpload}
                        beforeUpload={(file) => {
                          const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
                          if (!isJpgOrPng) {
                            message.error('只能上传 JPG/PNG 格式的图片');
                          }
                          const isLt2M = file.size / 1024 / 1024 < 2;
                          if (!isLt2M) {
                            message.error('图片大小不能超过 2MB');
                          }
                          return isJpgOrPng && isLt2M;
                        }}
                      >
                        <div>
                          <Avatar
                            size={80}
                            src={userProfile?.avatar}
                            icon={<UserOutlined />}
                          />
                          <div style={{ marginTop: '8px' }}>
                            <CameraOutlined /> 更换头像
                          </div>
                        </div>
                      </Upload>
                    </div>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input prefix={<UserOutlined />} disabled />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' },
                        ]}
                      >
                        <Input prefix={<MailOutlined />} />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="full_name"
                        label="姓名"
                        rules={[{ required: true, message: '请输入姓名' }]}
                      >
                        <Input placeholder="请输入真实姓名" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="手机号"
                        rules={[
                          { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' },
                        ]}
                      >
                        <Input prefix={<PhoneOutlined />} placeholder="请输入手机号" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="company" label="公司">
                        <Input placeholder="请输入公司名称" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="position" label="职位">
                        <Input placeholder="请输入职位" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="location" label="所在地">
                        <Input prefix={<HomeOutlined />} placeholder="请输入所在地" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="website" label="个人网站">
                        <Input placeholder="请输入个人网站" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item name="bio" label="个人简介">
                    <TextArea
                      rows={4}
                      placeholder="介绍一下自己..."
                      maxLength={200}
                      showCount
                    />
                  </Form.Item>

                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit" loading={loading}>
                        保存修改
                      </Button>
                      <Button onClick={() => setPasswordModalVisible(true)}>
                        修改密码
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </TabPane>

            <TabPane tab="登录历史" key="history">
              <Card title="最近登录记录">
                <List
                  dataSource={loginHistory}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            icon={<HistoryOutlined />}
                            style={{ backgroundColor: item.success ? '#52c41a' : '#ff4d4f' }}
                          />
                        }
                        title={
                          <Space>
                            <Text>{item.device}</Text>
                            <Tag color={item.success ? 'green' : 'red'}>
                              {item.success ? '成功' : '失败'}
                            </Tag>
                          </Space>
                        }
                        description={
                          <Space direction="vertical" size="small">
                            <Text type="secondary">IP: {item.ip_address}</Text>
                            <Text type="secondary">位置: {item.location}</Text>
                            <Text type="secondary">
                              时间: {new Date(item.login_time).toLocaleString()}
                            </Text>
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </TabPane>
          </Tabs>
        </Col>

        {/* 右侧信息卡片 */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            {/* 资料完整度 */}
            <Card title="资料完整度" size="small">
              <div style={{ textAlign: 'center', marginBottom: '16px' }}>
                <Progress
                  type="circle"
                  percent={completeness}
                  format={percent => `${percent}%`}
                />
              </div>
              <List
                size="small"
                dataSource={completenessItems}
                renderItem={(item) => (
                  <List.Item>
                    <Space>
                      <Text>{item.label}</Text>
                      {item.completed ? (
                        <Tag color="green">已完成</Tag>
                      ) : (
                        <Tag color="orange">待完善</Tag>
                      )}
                    </Space>
                  </List.Item>
                )}
              />
            </Card>

            {/* 使用统计 */}
            <Card title="使用统计" size="small">
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="订阅数量"
                    value={userProfile?.subscription_count || 0}
                    prefix={<TrophyOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="阅读新闻"
                    value={userProfile?.news_read_count || 0}
                    prefix={<EditOutlined />}
                  />
                </Col>
              </Row>
              <Divider />
              <Statistic
                title="获得点赞"
                value={userProfile?.total_likes || 0}
                prefix={<TrophyOutlined />}
              />
            </Card>

            {/* 账户信息 */}
            <Card title="账户信息" size="small">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text type="secondary">注册时间</Text>
                  <br />
                  <Text>{userProfile?.created_at ? new Date(userProfile.created_at).toLocaleDateString() : '-'}</Text>
                </div>
                <div>
                  <Text type="secondary">最后登录</Text>
                  <br />
                  <Text>{userProfile?.last_login ? new Date(userProfile.last_login).toLocaleString() : '-'}</Text>
                </div>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handlePasswordChange}
        >
          <Form.Item
            name="current_password"
            label="当前密码"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password prefix={<SafetyOutlined />} />
          </Form.Item>

          <Form.Item
            name="new_password"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码至少8个字符' },
            ]}
          >
            <Input.Password prefix={<SafetyOutlined />} />
          </Form.Item>

          <Form.Item
            name="confirm_password"
            label="确认新密码"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<SafetyOutlined />} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确认修改
              </Button>
              <Button onClick={() => setPasswordModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProfilePage;
