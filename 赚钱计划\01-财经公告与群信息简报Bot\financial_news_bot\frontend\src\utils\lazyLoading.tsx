import React, { Suspense, ComponentType, LazyExoticComponent } from 'react';
import { Spin } from 'antd';

// 懒加载配置
interface LazyLoadConfig {
  fallback?: React.ComponentType;
  retryCount?: number;
  retryDelay?: number;
  preload?: boolean;
  chunkName?: string;
}

// 错误边界组件
class LazyLoadErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy load error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return <FallbackComponent />;
    }

    return this.props.children;
  }
}

// 默认错误回退组件
const DefaultErrorFallback: React.FC = () => (
  <div style={{ 
    padding: '20px', 
    textAlign: 'center', 
    color: '#999',
    border: '1px dashed #d9d9d9',
    borderRadius: '4px'
  }}>
    <p>组件加载失败</p>
    <button onClick={() => window.location.reload()}>重新加载</button>
  </div>
);

// 默认加载组件
const DefaultLoadingFallback: React.FC = () => (
  <div style={{ 
    padding: '20px', 
    textAlign: 'center',
    minHeight: '100px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }}>
    <Spin size="large" />
  </div>
);

// 重试机制的懒加载
function lazyWithRetry<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  config: LazyLoadConfig = {}
): LazyExoticComponent<T> {
  const {
    retryCount = 3,
    retryDelay = 1000,
    chunkName
  } = config;

  return React.lazy(() => {
    return new Promise<{ default: T }>((resolve, reject) => {
      let attempts = 0;

      const attemptImport = async () => {
        try {
          const module = await importFunc();
          resolve(module);
        } catch (error) {
          attempts++;
          
          if (attempts >= retryCount) {
            reject(error);
            return;
          }

          console.warn(`Lazy load attempt ${attempts} failed, retrying in ${retryDelay}ms...`);
          
          setTimeout(() => {
            attemptImport();
          }, retryDelay * attempts); // 递增延迟
        }
      };

      attemptImport();
    });
  });
}

// 带预加载的懒加载
function lazyWithPreload<T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  config: LazyLoadConfig = {}
): LazyExoticComponent<T> & { preload: () => Promise<{ default: T }> } {
  let modulePromise: Promise<{ default: T }> | null = null;

  const load = () => {
    if (!modulePromise) {
      modulePromise = importFunc();
    }
    return modulePromise;
  };

  const LazyComponent = React.lazy(load);

  // 添加预加载方法
  (LazyComponent as any).preload = load;

  // 添加 chunkName 属性
  if (config.chunkName) {
    (LazyComponent as any).chunkName = config.chunkName;
  }

  // 如果配置了自动预加载
  if (config.preload) {
    // 在空闲时预加载
    if ('requestIdleCallback' in window) {
      requestIdleCallback(load);
    } else {
      setTimeout(load, 0);
    }
  }

  return LazyComponent as LazyExoticComponent<T> & { preload: () => Promise<{ default: T }>; chunkName?: string };
}

// 高阶组件：为懒加载组件添加错误边界和加载状态
export function withLazyLoading<P extends object>(
  LazyComponent: LazyExoticComponent<ComponentType<P>> & { preload?: () => Promise<any>; chunkName?: string },
  config: LazyLoadConfig = {}
) {
  const {
    fallback: LoadingFallback = DefaultLoadingFallback,
  } = config;

  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <LazyLoadErrorBoundary fallback={config.fallback}>
      <Suspense fallback={<LoadingFallback />}>
        <LazyComponent {...props} ref={ref} />
      </Suspense>
    </LazyLoadErrorBoundary>
  ));

  // 保留原始组件的 preload 方法和 chunkName
  if (LazyComponent.preload) {
    (WrappedComponent as any).preload = LazyComponent.preload;
  }
  if (LazyComponent.chunkName || config.chunkName) {
    (WrappedComponent as any).chunkName = LazyComponent.chunkName || config.chunkName;
  }

  return WrappedComponent;
}

// 路由级别的懒加载
export const createLazyRoute = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  config: LazyLoadConfig = {}
) => {
  const LazyComponent = lazyWithRetry(importFunc, config);
  return withLazyLoading(LazyComponent, config);
};

// 组件级别的懒加载
export const createLazyComponent = <T extends ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  config: LazyLoadConfig = {}
) => {
  const LazyComponent = lazyWithPreload(importFunc, config);
  return withLazyLoading(LazyComponent, config);
};

// 预定义的懒加载页面组件
export const LazyPages = {
  // 认证页面
  LoginPage: createLazyRoute(
    () => import('../pages/auth/LoginPage'),
    { chunkName: 'auth' }
  ),
  RegisterPage: createLazyRoute(
    () => import('../pages/auth/RegisterPage'),
    { chunkName: 'auth' }
  ),
  ForgotPasswordPage: createLazyRoute(
    () => import('../pages/auth/ForgotPasswordPage'),
    { chunkName: 'auth' }
  ),

  // 主要页面
  DashboardPage: createLazyRoute(
    () => import('../pages/dashboard/DashboardPage'),
    { chunkName: 'dashboard', preload: true }
  ),
  NewsPage: createLazyRoute(
    () => import('../pages/news/NewsPage'),
    { chunkName: 'news', preload: true }
  ),
  NewsDetailPage: createLazyRoute(
    () => import('../pages/news/NewsDetailPage'),
    { chunkName: 'news' }
  ),
  BookmarksPage: createLazyRoute(
    () => import('../pages/news/BookmarksPage'),
    { chunkName: 'news' }
  ),

  // 订阅页面
  SubscriptionPage: createLazyRoute(
    () => import('../pages/subscription/SubscriptionPage'),
    { chunkName: 'subscription' }
  ),
  SubscriptionCreatePage: createLazyRoute(
    () => import('../pages/subscription/SubscriptionCreatePage'),
    { chunkName: 'subscription' }
  ),
  SubscriptionEditPage: createLazyRoute(
    () => import('../pages/subscription/SubscriptionEditPage'),
    { chunkName: 'subscription' }
  ),

  // 用户页面
  ProfilePage: createLazyRoute(
    () => import('../pages/profile/ProfilePage'),
    { chunkName: 'profile' }
  ),
  SettingsPage: createLazyRoute(
    () => import('../pages/settings/SettingsPage'),
    { chunkName: 'settings' }
  ),

  // 错误页面
  NotFoundPage: createLazyRoute(
    () => import('../pages/error/NotFoundPage'),
    { chunkName: 'error' }
  ),
};

// 预定义的懒加载组件
export const LazyComponents = {
  // 响应式表格
  ResponsiveTable: createLazyComponent(
    () => import('../components/common/ResponsiveTable'),
    { chunkName: 'table' }
  ),

  // 全局搜索
  GlobalSearch: createLazyComponent(
    () => import('../components/search/GlobalSearch'),
    { chunkName: 'search' }
  ),

  // 用户指南
  UserGuide: createLazyComponent(
    () => import('../components/guide/UserGuide'),
    { chunkName: 'guide' }
  ),
};

// 预加载管理器
class PreloadManager {
  private preloadedChunks = new Set<string>();
  private preloadPromises = new Map<string, Promise<any>>();

  // 预加载指定的组件
  preload(components: Array<{ preload?: () => Promise<any>; chunkName?: string }>) {
    components.forEach(component => {
      if (component.preload && !this.preloadedChunks.has(component.chunkName || 'unknown')) {
        const promise = component.preload();
        if (component.chunkName) {
          this.preloadPromises.set(component.chunkName, promise);
          this.preloadedChunks.add(component.chunkName);
        }
      }
    });
  }

  // 根据路由预加载相关组件
  preloadForRoute(route: string) {
    const routePreloadMap: Record<string, () => void> = {
      '/dashboard': () => import('../pages/dashboard/DashboardPage').catch(() => {}),
      '/news': () => {
        import('../pages/news/NewsPage').catch(() => {});
        import('../components/common/ResponsiveTable').catch(() => {});
      },
      '/subscriptions': () => import('../pages/subscription/SubscriptionPage').catch(() => {}),
      '/profile': () => import('../pages/profile/ProfilePage').catch(() => {}),
      '/settings': () => import('../pages/settings/SettingsPage').catch(() => {}),
    };

    const preloadFn = routePreloadMap[route];
    if (preloadFn) {
      preloadFn();
    }
  }

  // 在空闲时预加载常用组件
  preloadOnIdle() {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        // 直接预加载模块，不依赖组件的 preload 方法
        import('../pages/dashboard/DashboardPage').catch(() => {});
        import('../pages/news/NewsPage').catch(() => {});
        import('../components/common/ResponsiveTable').catch(() => {});
      });
    }
  }

  // 获取预加载状态
  getPreloadStatus() {
    return {
      preloadedChunks: Array.from(this.preloadedChunks),
      pendingPromises: this.preloadPromises.size,
    };
  }
}

export const preloadManager = new PreloadManager();

// 在应用启动时预加载关键组件
export const initializePreloading = () => {
  // 预加载关键页面
  preloadManager.preloadOnIdle();
  
  // 监听路由变化进行预加载
  if (typeof window !== 'undefined') {
    window.addEventListener('popstate', () => {
      preloadManager.preloadForRoute(window.location.pathname);
    });
  }
};

export { lazyWithRetry, lazyWithPreload };
